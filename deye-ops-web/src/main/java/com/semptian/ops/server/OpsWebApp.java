package com.semptian.ops.server;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.annotation.Resource;

/**
 * web application
 *
 * <AUTHOR>
 * @date 2025/2/20 下午12:27
 */
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@ComponentScan("com.semptian.*")
@MapperScan("com.semptian.mapper")
@EnableAsync
@EnableFeignClients(basePackages = {"com.semptian.*"})
@EnableDiscoveryClient
@EnableSwagger2
public class OpsWebApp {
    @Resource
    private RestTemplateBuilder builder;

    @Bean
    public RestTemplate restTemplate() {
        return builder.build();
    }

    public static void main(String[] args) {
        SpringApplication.run(OpsWebApp.class, args);
    }

}