DROP TABLE IF EXISTS ops_api_access_result_difference;
CREATE TABLE ops_api_access_result_difference (
  `metric_id` varchar(64) NOT NULL COMMENT '指标ID',
  `metric_name` varchar(256) NOT NULL COMMENT '指标名称',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `account` varchar(1024) NOT NULL COMMENT '操作账号',
  `is_kv` tinyint NOT NULL COMMENT '是否是key-value对比，true为kv',
  `kv_content` text NULL COMMENT '响应的kv数据，json格式。会包含多个系统的kv数据',
  `different_kv_content` text NULL COMMENT '对比后有差异的维度，多个使用逗号拼接'
) ENGINE=OLAP
UNIQUE KEY(`metric_id`, `metric_name`, `stat_date`, `account`)
COMMENT '系统间一致性统计结果差异表'
AUTO PARTITION BY RANGE (date_trunc(`stat_date`, 'day')) ()
DISTRIBUTED BY HASH(`metric_id`, `stat_date`) BUCKETS 3
PROPERTIES (
"replication_allocation" = "tag.location.default: 2"
);