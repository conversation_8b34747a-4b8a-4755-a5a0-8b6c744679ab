package com.semptian.model.menu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/2/28 14:18
 */
@Data
@ApiModel
public class MenuModel {

    @ApiModelProperty(value = "功能id,当有功能id的时候为编辑，无id时为添加", example = "3221312131")
    private String id;

    @ApiModelProperty(value = "功能名称", example = "app1")
    private String menuName;

    @ApiModelProperty(value = "功能编码", example = "icon")
    private String menuCode;

    @ApiModelProperty(value = "功能url", example = "http://32.32.3.3:8080/")
    private String menuUrl;

    @ApiModelProperty(value = "功能备注", example = "rerwr")
    private String menuDesc;

    @ApiModelProperty(value = "功能所属应用", example = "13243242")
    private Integer appId;

    @ApiModelProperty(value = "功能所属应用名称", example = "13243242")
    private String appName;

    @ApiModelProperty(value = "功能父节点，默认0", example = "0")
    private String parentId;

    @ApiModelProperty(value = "当前登录用户")
    private String curUser;
}
