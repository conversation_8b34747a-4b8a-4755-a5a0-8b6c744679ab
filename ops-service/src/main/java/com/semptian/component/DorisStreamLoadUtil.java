package com.semptian.component;// Licensed to the Apache Software Foundation (ASF) under one

import cn.hutool.core.date.DateUtil;
import com.semptian.annotation.OperateMock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultRedirectStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.FileReader;
import java.io.IOException;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2024/6/4 下午12:27
 * Description: Doris使用StreamLoad写入
 */
@Component
@Slf4j
public class DorisStreamLoadUtil {
    @Value("${doris.host}")
    private String DORIS_HOST = "************";

    @Value("${doris.db}")
    private String DORIS_DB = "ads_ops";

    @Value("${doris.user}")
    private String DORIS_USER = "root";

    @Value("${doris.password}")
    private String DORIS_PASSWORD = "123456";

    @Value("${doris.port:8030}")
    private Integer DORIS_HTTP_PORT = 8030;

    /**
     * doris写入异常场景配置
     */
    @Value("${doris.write.error:Partition does not exist,ErrorURL}")
    private List<String> SyncWriteErrorMapList;


    public void sendDataWithParam(String content, String table) throws Exception {
        DORIS_USER = "root";
        DORIS_PASSWORD = "123456";
        final String loadUrl = String.format("http://%s:%s/api/%s/%s/_stream_load", "*************", "8030", "ads_netsec", table);
        send(content, loadUrl, table);
    }

    @OperateMock
    private void send(String content, String loadUrl, String table) throws IOException {
        final HttpClientBuilder httpClientBuilder = HttpClients.custom().setRedirectStrategy(new DefaultRedirectStrategy() {
            @Override
            protected boolean isRedirectable(String method) {
                return true;
            }
        });

        try (CloseableHttpClient client = httpClientBuilder.build()) {
            HttpPut put = new HttpPut(loadUrl);
            StringEntity entity = new StringEntity(content, "UTF-8");
            put.setHeader(HttpHeaders.EXPECT, "100-continue");
            put.setHeader(HttpHeaders.AUTHORIZATION, basicAuthHeader(DORIS_USER, DORIS_PASSWORD));
            // the label header is optional, not necessary
            // use label header can ensure at most once semantics
            //put.setHeader("label", "39c25a5c-7000-496e-a98e-348a264c81de");
            put.setHeader("label", UUID.randomUUID().toString());
            put.setEntity(entity);

            try (CloseableHttpResponse response = client.execute(put)) {
                String loadResult;
                if (response.getEntity() != null) {
                    loadResult = EntityUtils.toString(response.getEntity());
                } else {
                    loadResult = "";
                }
                final int statusCode = response.getStatusLine().getStatusCode();
                // statusCode 200 just indicates that doris be service is ok, not stream load
                // you should see the output content to find whether stream load is success
                if (statusCode != 200) {
                    throw new IOException(String.format("Stream load failed, statusCode=%s load result=%s", statusCode, loadResult));
                }
                log.info("Stream load success, load result: {}", loadResult);
                //对load结果进行处理，区分不同的异常，进行数据保存
                if (!loadResult.contains("Success")) {
                    log.error("Stream load failed, load result: {}", loadResult);
                    //写入到SyncWriteErrorMap队列中(部分场景，例如分区不存在，或者数据质量导致写入失败),对比SyncWriteErrorMapList
                    AtomicBoolean isError = new AtomicBoolean(false);
                    SyncWriteErrorMapList.forEach(e -> {
                        if (loadResult.contains(e)) {
                            isError.set(true);
                        }
                    });
                }
            } catch (Exception e) {
                log.error("Stream load failed", e);

            }
        }
    }

    public void sendData(String content, String table) throws Exception {
        final String loadUrl = String.format("http://%s:%s/api/%s/%s/_stream_load", DORIS_HOST, DORIS_HTTP_PORT, DORIS_DB, table);
        send(content, loadUrl, table);
    }

    private String basicAuthHeader(String username, String password) {
        final String tobeEncode = username + ":" + password;
        byte[] encoded = Base64.encodeBase64(tobeEncode.getBytes(StandardCharsets.UTF_8));
        return "Basic " + new String(encoded);
    }

    public static void main(String[] args) throws Exception {
        /**
         * 目标表结构
         * CREATE TABLE `ads_network_security_detecte_user` (
         *   `strsrc_ip` VARCHAR(64) NULL COMMENT '字符型源IP',
         *   `host` VARCHAR(64) NULL COMMENT '访问域名',
         *   `data_type` INT NULL COMMENT '协议类型 malice_url：1208  botnet：1209 dos：1210 gateway_antivirus：1211',
         *   `user_group` VARCHAR(20) NULL COMMENT '用户组',
         *   `user_name` VARCHAR(64) NULL COMMENT '用户名',
         *   `src_port` INT NULL COMMENT '源端口',
         *   `src_country` VARCHAR(20) NULL COMMENT '源ip所在国家',
         *   `strdst_ip` VARCHAR(64) NULL COMMENT '字符型目的IP',
         *   `dst_port` INT NULL COMMENT '目标端口',
         *   `dst_country` VARCHAR(20) NULL COMMENT '目的ip所在国家',
         *   `infected_level` INT NULL COMMENT '感染等级',
         *   `risk_level` INT NULL COMMENT '风险等级',
         *   `virus` VARCHAR(64) NULL COMMENT '病毒名称',
         *   `family` VARCHAR(64) NULL COMMENT '病毒小类',
         *   `hot_event` VARCHAR(64) NULL COMMENT '热点事件类型',
         *   `category` VARCHAR(64) NULL COMMENT '分类',
         *   `action` VARCHAR(64) NULL COMMENT '访问控制',
         *   `dev_id` VARCHAR(64) NULL COMMENT '设备id，区分不同的设备',
         *   `terminal_type` VARCHAR(64) NULL COMMENT '终端类型',
         *   `url` VARCHAR(256) NULL COMMENT '访问url',
         *   `app_type` VARCHAR(64) NULL COMMENT '应用类型',
         *   `app_name` VARCHAR(64) NULL COMMENT '应用名',
         *   `protocol_id` VARCHAR(64) NULL COMMENT '协议号',
         *   `packet` VARCHAR(64) NULL COMMENT '数据包',
         *   `capture_time` BIGINT NULL COMMENT '截获时间',
         *   `uparea_id` INT NULL COMMENT '上报地ID',
         *   `insert_time` BIGINT NULL COMMENT '入库时间',
         *   `data_id` VARCHAR(64) NULL COMMENT '数据id',
         *   `sigclass` VARCHAR(64) NULL,
         *   `signame` VARCHAR(64) NULL,
         *   `capture_day` DATE NOT NULL COMMENT '一级分区',
         *   `capture_hour` INT NULL COMMENT '二级分区'
         * ) ENGINE=OLAP
         */
        //读取本地CSV文件
        DorisStreamLoadUtil dorisStreamLoad = new DorisStreamLoadUtil();
        String csvFile = "G:\\dws_network_security_detecte_all_202407161104.csv";
        int batchSize = 0;
        StringBuilder content = new StringBuilder();
        try (Reader in = new FileReader(csvFile)) {
            Iterable<CSVRecord> records = CSVFormat.DEFAULT.withFirstRecordAsHeader().parse(in);
            // Loop over the CSV records
            for (CSVRecord record : records) {
                // Replace these with the actual column names
                String dataType = record.get("data_type");
                String userGroup = record.get("user_group");
                String userName = record.get("user_name");
                String strsrcIp = record.get("strsrc_ip");
                String srcPort = record.get("src_port");
                String strdstIp = record.get("strdst_ip");
                String dstPort = record.get("dst_port");
                String infectedLevel = record.get("infected_level");
                String riskLevel = record.get("risk_level");
                String virus = record.get("virus");
                String family = record.get("family");
                String hotEvent = record.get("hot_event");
                String category = record.get("category");
                String action = record.get("action");
                String devId = record.get("dev_id");
                String terminalType = record.get("terminal_type");
                String url = record.get("url");
                String host = record.get("host");
                String appType = record.get("app_type");
                String appName = record.get("app_name");
                String protocolId = record.get("protocol_id");
                String packet = "packet";
                //captureTime 随机取从今天到过去一个月的随机时间
                String captureTime = String.valueOf(System.currentTimeMillis() - (long) (Math.random() * 30 * 24 * 60 * 60 * 1000));
                //String captureTime = record.get("capture_time");
                String upareaId = record.get("uparea_id");
                String insertTime = String.valueOf(System.currentTimeMillis());
                String dataId = record.get("data_id");
                String sigclass = record.get("sigclass");
                String signame = record.get("signame");
                //captureDay根据captureTime计算yyyy-MM-dd格式
                String captureDay = DateUtil.format(DateUtil.date(Long.parseLong(captureTime)), "yyyy-MM-dd");
                //captureDay根据captureTime计算HH
                String captureHour = DateUtil.format(DateUtil.date(Long.parseLong(captureTime)), "HH");
                String srcCountry = record.get("src_country");
                String dstCountry = record.get("dst_country");
                //按照目标表的结构拼接成一行数据
                content.append(strsrcIp).append("\t").append(host).append("\t").append(dataType).append("\t").append(userGroup).append("\t").append(userName).append("\t").append(srcPort).append("\t").append(srcCountry).append("\t").append(strdstIp).append("\t").append(dstPort).append("\t").append(dstCountry).append("\t").append(infectedLevel).append("\t").append(riskLevel).append("\t").append(virus).append("\t").append(family).append("\t").append(hotEvent).append("\t").append(category).append("\t").append(action).append("\t").append(devId).append("\t").append(terminalType).append("\t").append(url).append("\t").append(appType).append("\t").append(appName).append("\t").append(protocolId).append("\t").append(packet).append("\t").append(captureTime).append("\t").append(upareaId).append("\t").append(insertTime).append("\t").append(dataId).append("\t").append(sigclass).append("\t").append(signame).append("\t").append(captureDay).append("\t").append(captureHour).append("\n");
                batchSize++;

            }
        } catch (Exception e) {
            System.out.println("Error in CsvFileReader !!!");
        }
    }


}