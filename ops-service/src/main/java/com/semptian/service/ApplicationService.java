package com.semptian.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.ApplicationEntity;
import com.semptian.model.application.ApplicationModel;
import com.semptian.model.application.ApplicationStateModel;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/1
 * @Description
 **/
public interface ApplicationService  {


    /**
     * 根据appid获取该应用下的所有菜单以及操作信息
     *
     * @param keyword     关键字
     * @param i18nKeyword
     * @param appId       应用id
     * @param userId      用户id
     * @param isAuth      是否进行权限控制
     * @return
     */
    Map getMenuAndOperateByAppid(String keyword, String[] i18nKeyword, String appId, String userId, boolean isAuth,boolean allPermission);


}