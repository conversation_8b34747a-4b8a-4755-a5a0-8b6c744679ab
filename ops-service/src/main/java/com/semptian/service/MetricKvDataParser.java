package com.semptian.service;

import com.semptian.entity.BusinessApiConfig;
import com.semptian.entity.OpsApiAccessResult;

import java.util.List;
import java.util.Map;

/**
 * 指标KV数据解析接口
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
public interface MetricKvDataParser {

    void parseData(List<BusinessApiConfig> apiConfigList, OpsApiAccessResult opsApiAccessResult, Map<String, Map<String, Integer>> systemKvMap);

    List<Integer> getMetricIds();

    List<String> getSystemNames();
}
