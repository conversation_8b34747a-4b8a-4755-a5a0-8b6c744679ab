package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.BusinessAccountConfig;
import com.semptian.entity.BusinessMetricConfig;

import java.util.List;

public interface BusinessAccountConfigService extends IService<BusinessAccountConfig> {
    Page<BusinessAccountConfig> getAccountConfigList(int pageNum, int pageSize, Integer sysId, String accountType, Boolean enable);

    List<BusinessAccountConfig> getAccountConfigByMetricId(BusinessMetricConfig businessMetricConfig, Integer maxSampleSize, String collectTimeRange);

    List<BusinessAccountConfig> getAccountConfigByMetricIdWithStaticAccount(BusinessMetricConfig businessMetricConfig, Integer maxSampleSize, String collectTimeRange);
}