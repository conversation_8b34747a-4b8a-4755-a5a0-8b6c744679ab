package com.semptian.service.impl.parser;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.semptian.common.SystemEnum;
import com.semptian.entity.BusinessApiConfig;
import com.semptian.entity.OpsApiAccessResult;
import com.semptian.service.MetricKvDataParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 指标5综合搜索系统 KV数据解析接口
 *
 * <AUTHOR> Hu
 * @since 2025/5/21
 */
@Component
@Slf4j
public class MetricKvDataSpecial1Parser implements MetricKvDataParser {


    @Override
    public void parseData(List<BusinessApiConfig> apiConfigList, OpsApiAccessResult result, Map<String, Map<String, Integer>> systemKvMap) {
        //指标5的综合搜索kv数据数据已经是对应好的kv结构,不需要再解析

        if (StrUtil.isEmpty(result.getKvContent())) {
            log.warn("System {}, account {}, statDate{}, has no  kvContent", result.getSystemName(), result.getAccount(), result.getStatDate());
            return;
        }

        String systemName = result.getSystemName();

        try {
            // 解析kv_content字段
            JSONArray kvContentArray = JSONArray.parseArray(result.getKvContent());

            // 存储解析后的数据
            Map<String, Integer> parsedKvData = new HashMap<>();

            // 解析每个kv数据
            for (Object kvObj : kvContentArray) {
                JSONObject kvJson = (JSONObject) kvObj;

                for (String key : kvJson.keySet()) {
                    if (StrUtil.isNotEmpty(key)) {

                        Integer countValue = StrUtil.isEmptyIfStr(kvJson.get(key)) ? 0 : kvJson.getIntValue(key);

                        if (parsedKvData.containsKey(key)) {
                            // 如果已经存在该key,则把值累加
                            parsedKvData.put(key, parsedKvData.get(key) + countValue);
                        } else {
                            parsedKvData.put(key, countValue);
                        }
                    }
                }
            }

            // 存储解析后的数据
            systemKvMap.put(result.getSystemName(), parsedKvData);
        }catch (Exception e) {
            log.error("System {},account {}, statDate{}, parse kv_content:{}, error", systemName, result.getAccount(), result.getStatDate(), result.getKvContent(), e);
        }
    }

    @Override
    public List<Integer> getMetricIds() {
        return Lists.newArrayList(5);
    }

    @Override
    public List<String> getSystemNames() {
        return Lists.newArrayList(SystemEnum.SEARCH.getChineseName());
    }
}
