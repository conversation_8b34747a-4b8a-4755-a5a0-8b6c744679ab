package com.semptian.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.semptian.component.FileSystemUtil;
import com.semptian.component.HBaseQueryUtil;
import com.semptian.component.RedisLockUtil;
import com.semptian.entity.DetailFieldConfig;
import com.semptian.entity.DetailQueryConfig;
import com.semptian.entity.OpsApiAccessResultDifference;
import com.semptian.mapper.DetailFieldConfigMapper;
import com.semptian.mapper.DetailQueryConfigMapper;
import com.semptian.mapper.OpsApiAccessResultDifferenceMapper;
import com.semptian.service.DetailExportService;
import com.semptian.service.ElasticsearchService;
import com.semptian.service.OpsApiAccessResultDifferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 明细导出服务实现类
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@Service
public class DetailExportServiceImpl implements DetailExportService {

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private OpsApiAccessResultDifferenceService opsApiAccessResultDifferenceService;

    @Resource
    private DetailQueryConfigMapper detailQueryConfigMapper;

    @Resource
    private DetailFieldConfigMapper detailFieldConfigMapper;

    @Resource
    private ElasticsearchService elasticsearchService;

    @Resource
    private HBaseQueryUtil hBaseQueryUtil;

    @Resource
    private FileSystemUtil fileSystemUtil;

    @Override
    public String exportHBaseDetail(Integer metricId, LocalDate startDate, LocalDate endDate) {
        String lockKey = "detail_export_1" + metricId + "_" + startDate + "_" + endDate;

        // 1. 获取分布式锁
        if (!redisLockUtil.tryLockByValidityTime(lockKey)) {
            String message = String.format("指标[%d]在时间范围[%s-%s]的导出任务正在执行中", metricId, startDate, endDate);
            log.warn(message);
            return message;
        }

        try {
            // 2. 检查磁盘空间
//            if (!fileSystemUtil.checkDiskUsage()) {
//                throw new RuntimeException("磁盘使用率超过70%，无法执行导出任务");
//            }

            // 3. 清理文件
            fileSystemUtil.cleanupFiles(metricId);

            // 4. 执行导出流程
            return executeExportProcess(metricId, startDate, endDate);

        } catch (Exception e) {
            log.error("[明细导出] 导出失败 | 指标ID: {} | 时间范围: {}-{} | 错误: {}",
                     metricId, startDate, endDate, e.getMessage(), e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        } finally {
            // 5. 释放锁
            redisLockUtil.unlock(lockKey);
        }
    }

    /**
     * 执行导出流程
     */
    private String executeExportProcess(Integer metricId, LocalDate startDate, LocalDate endDate) {
        log.info("[明细导出] 开始执行导出流程 | 指标ID: {} | 时间范围: {}-{}", metricId, startDate, endDate);

        // 1. 读取差异账号
        List<OpsApiAccessResultDifference> differenceList = getDifferenceAccounts(metricId, startDate, endDate);
        if (differenceList.isEmpty()) {
            String message = String.format("指标[%d]在时间范围[%s-%s]没有找到差异账号", metricId, startDate, endDate);
            log.info(message);
            return message;
        }

        log.info("[明细导出] 找到差异账号数量: {}", differenceList.size());

        // 2. 读取DSL配置
        DetailQueryConfig queryConfig = detailQueryConfigMapper.getByMetricId(metricId);
        if (queryConfig == null) {
            throw new RuntimeException("未找到指标[" + metricId + "]的DSL查询配置");
        }

        // 3. 提取账号列表
        List<String> accounts = differenceList.stream()
                .map(OpsApiAccessResultDifference::getAccount)
                .distinct()
                .collect(Collectors.toList());

        // 4. 执行ES查询获取data_id和协议信息
        Map<String, List<Map<String, Object>>> dataByProtocol = executeEsQueryAndGroupByProtocol(accounts, queryConfig, startDate, endDate);

        int totalExportedFiles = 0;
        for (Map.Entry<String, List<Map<String, Object>>> entry : dataByProtocol.entrySet()) {
            String protocolType = entry.getKey();
            List<Map<String, Object>> esData = entry.getValue();

            try {
                // 5. 执行单个协议的导出
                boolean exported = exportProtocolData(metricId, protocolType, esData, startDate, endDate);
                if (exported) {
                    totalExportedFiles++;
                }
            } catch (Exception e) {
                log.error("[明细导出] 协议[{}]导出失败: {}", protocolType, e.getMessage(), e);
            }
        }

        String message = String.format("导出完成 | 指标ID: %d | 协议数: %d | 成功导出文件数: %d",
                                      metricId, dataByProtocol.size(), totalExportedFiles);
        log.info(message);
        return message;
    }

    /**
     * 获取差异账号 - 使用原生SQL查询
     */
    private List<OpsApiAccessResultDifference> getDifferenceAccounts(Integer metricId, LocalDate startDate, LocalDate endDate) {
        try {
            log.debug("[差异账号查询] 开始查询 | 指标ID: {} | 时间范围: {}-{}", metricId, startDate, endDate);

            List<OpsApiAccessResultDifference> results = opsApiAccessResultDifferenceService.selectDifferenceAccountsBySql(
                    metricId,
                    Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                    Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant())
            );

            log.debug("[差异账号查询] 查询完成 | 指标ID: {} | 结果数量: {}", metricId, results.size());
            return results;

        } catch (Exception e) {
            log.error("[差异账号查询] 查询失败 | 指标ID: {} | 时间范围: {}-{} | 错误: {}",
                     metricId, startDate, endDate, e.getMessage(), e);
            throw new RuntimeException("查询差异账号失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行ES查询并按协议类型分组
     */
    private Map<String, List<Map<String, Object>>> executeEsQueryAndGroupByProtocol(List<String> accounts,
                                                                                   DetailQueryConfig queryConfig,
                                                                                   LocalDate startDate,
                                                                                   LocalDate endDate) {
        try {
            // 1. 构建DSL查询
            String dslTemplate = queryConfig.getParamsTemplate();
            String indices = queryConfig.getReqPath();

            // 2. 替换模板中的参数
            String dsl = replaceDslParameters(dslTemplate, accounts, startDate, endDate);

            // 3. 执行查询
            JSONObject response = elasticsearchService.executeDslQuery(indices, dsl);

            // 4. 解析响应并按协议类型分组
            return parseEsResponseAndGroupByProtocol(response, queryConfig.getResponseMapping());

        } catch (Exception e) {
            log.error("[ES查询] 查询失败: {}", e.getMessage(), e);
            throw new RuntimeException("ES查询失败", e);
        }
    }

    /**
     * 解析ES响应并按协议类型分组
     */
    private Map<String, List<Map<String, Object>>> parseEsResponseAndGroupByProtocol(JSONObject response, String responseMapping) {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();

        try {
            JSONObject hits = response.getJSONObject("hits");
            JSONArray hitsArray = hits.getJSONArray("hits");

            // 解析响应映射配置
            JSONObject mapping = JSON.parseObject(responseMapping);
            String dataIdField = mapping.getString("data_id");

            for (int i = 0; i < hitsArray.size(); i++) {
                JSONObject hit = hitsArray.getJSONObject(i);
                String index = hit.getString("_index");

                // 从索引名称中提取协议类型
                String protocolType = extractProtocolTypeFromIndex(index);

                // 构建数据对象
                Map<String, Object> dataItem = new HashMap<>();
                dataItem.put("data_id", hit.getString(dataIdField));
                dataItem.put("index", index);
                dataItem.put("protocol_type", protocolType);

                result.computeIfAbsent(protocolType, k -> new ArrayList<>()).add(dataItem);
            }

        } catch (Exception e) {
            log.error("[响应解析] 解析ES响应失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 从索引名称中提取协议类型
     * 例如：deye_v64_email_202505-000001 -> email
     */
    private String extractProtocolTypeFromIndex(String index) {
        try {
            // 索引格式：deye_v64_{protocol}_{date}-{suffix}
            String[] parts = index.split("_");
            if (parts.length >= 3) {
                return parts[2]; // 第三部分是协议类型
            }
        } catch (Exception e) {
            log.warn("[协议解析] 无法从索引名称解析协议类型: {}", index);
        }

        // 默认返回unknown
        return "unknown";
    }

    /**
     * 导出单个协议的数据
     */
    private boolean exportProtocolData(Integer metricId, String protocolType, List<Map<String, Object>> esData,
                                     LocalDate startDate, LocalDate endDate) {
        try {

            log.info("[明细导出] 开始导出协议[{}]数据 | ES记录数: {}", protocolType, esData.size());

            // 1. 提取data_id列表
            List<String> dataIds = esData.stream()
                    .map(item -> (String) item.get("data_id"))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (dataIds.isEmpty()) {
                log.warn("[明细导出] 协议[{}]未找到有效的data_id", protocolType);
                return false;
            }

            // 2. 批量查询HBase获取明细数据
            List<Map<String, Object>> hbaseData = queryHBaseData(protocolType, dataIds, startDate, endDate);
            if (hbaseData.isEmpty()) {
                log.warn("[明细导出] 协议[{}]未查询到HBase数据", protocolType);
                return false;
            }

            // 3. 获取字段配置
            List<DetailFieldConfig> fieldConfigs = detailFieldConfigMapper.getExportFieldsByDataType(protocolType);

            // 4. 合并ES数据和HBase数据
            List<Map<String, Object>> mergedData = mergeEsAndHBaseData(esData, hbaseData);

            // 5. 生成CSV文件
            generateCsvFile(metricId, protocolType, mergedData, fieldConfigs);

            log.info("[明细导出] 协议[{}]导出成功 | data_id数: {} | HBase记录数: {} | 合并记录数: {}",
                    protocolType, dataIds.size(), hbaseData.size(), mergedData.size());
            return true;

        } catch (Exception e) {
            log.error("[明细导出] 协议[{}]导出失败: {}", protocolType, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 合并ES数据和HBase数据
     */
    private List<Map<String, Object>> mergeEsAndHBaseData(List<Map<String, Object>> esData,
                                                         List<Map<String, Object>> hbaseData) {
        List<Map<String, Object>> mergedData = new ArrayList<>();

        // 创建HBase数据的索引，以data_id为key
        Map<String, Map<String, Object>> hbaseIndex = new HashMap<>();
        for (Map<String, Object> hbaseRow : hbaseData) {
            String dataId = extractDataIdFromRowKey((String) hbaseRow.get("rowkey"));
            if (dataId != null) {
                hbaseIndex.put(dataId, hbaseRow);
            }
        }

        // 合并数据
        for (Map<String, Object> esRow : esData) {
            String dataId = (String) esRow.get("data_id");
            Map<String, Object> hbaseRow = hbaseIndex.get(dataId);

            if (hbaseRow != null) {
                Map<String, Object> mergedRow = new HashMap<>();
                // 添加ES数据
                mergedRow.putAll(esRow);
                // 添加HBase数据
                mergedRow.putAll(hbaseRow);
                mergedData.add(mergedRow);
            }
        }

        return mergedData;
    }

    /**
     * 从HBase行键中提取data_id
     */
    private String extractDataIdFromRowKey(String rowKey) {
        try {
            // 根据实际的HBase行键格式来解析data_id
            // 这里假设行键格式包含data_id信息
            return rowKey;
        } catch (Exception e) {
            log.warn("[数据合并] 无法从行键解析data_id: {}", rowKey);
            return null;
        }
    }

    /**
     * 替换DSL模板参数
     */
    private String replaceDslParameters(String dslTemplate, List<String> accounts,
                                      LocalDate startDate, LocalDate endDate) {
        try {
            // 1. 解析DSL模板为JSONObject
            JSONObject dslObject = JSON.parseObject(dslTemplate);

            // 2. 获取dateFormat字段，判断时间格式
            String dateFormat = dslObject.getString("dateFormat");

            // 3. 移除dateFormat字段，避免Elasticsearch报错
            dslObject.remove("dateFormat");

            // 4. 构建账号查询条件
            String accountsJson = accounts.stream()
                    .map(account -> "\\\"" + account + "\\\"")
                    .collect(Collectors.joining(","));

            // 5. 将JSONObject转回字符串并替换参数
            String dsl = dslObject.toJSONString();

            // 6. 根据dateFormat替换时间参数
            if ("timestamp".equals(dateFormat)) {
                // 时间戳格式，将时间转化为13位时间戳，使用系统默认时区
                long startTimestamp = startDate.atStartOfDay().atZone(java.time.ZoneOffset.systemDefault()).toInstant().toEpochMilli();
                long endTimestamp = endDate.atStartOfDay().atZone(java.time.ZoneOffset.systemDefault()).toInstant().toEpochMilli() + 24 * 60 * 60 * 1000 - 1;

                dsl = dsl.replace("112233", String.valueOf(startTimestamp))
                        .replace("445566", String.valueOf(endTimestamp));

                log.debug("[DSL参数替换] 使用时间戳格式 | 开始时间戳: {} | 结束时间戳: {}", startTimestamp, endTimestamp);
            } else {
                // 字符串日期格式
                dsl = dsl.replace("#{startDay}", startDate.toString())
                        .replace("#{endDay}", endDate.toString());

                log.debug("[DSL参数替换] 使用字符串日期格式 | 开始日期: {} | 结束日期: {}", startDate, endDate);
            }

            // 7. 替换账号参数
            dsl = dsl.replace("#{account}", accounts.isEmpty() ? "" : accounts.get(0))
                    .replace("{accounts}", accountsJson);

            // 8. 替换其他通用时间参数
            dsl = dsl.replace("{startDate}", startDate.toString())
                    .replace("{endDate}", endDate.toString())
                    .replace("{startTime}", startDate.atStartOfDay().toString())
                    .replace("{endTime}", endDate.plusDays(1).atStartOfDay().toString());

            log.debug("[DSL参数替换] 原始模板: {}", dslTemplate);
            log.debug("[DSL参数替换] 处理后DSL: {}", dsl);

            return dsl;

        } catch (Exception e) {
            log.error("[DSL参数替换] 解析DSL模板失败: {}", e.getMessage(), e);
            // 如果解析失败，使用原始方式处理（向后兼容）
            String accountsJson = accounts.stream()
                    .map(account -> "\"" + account + "\"")
                    .collect(Collectors.joining(","));

            return dslTemplate
                    .replace("{accounts}", accountsJson)
                    .replace("{startDate}", startDate.toString())
                    .replace("{endDate}", endDate.toString())
                    .replace("{startTime}", startDate.atStartOfDay().toString())
                    .replace("{endTime}", endDate.plusDays(1).atStartOfDay().toString());
        }
    }



    /**
     * 查询HBase数据
     */
    private List<Map<String, Object>> queryHBaseData(String protocolType, List<String> dataIds,
                                                   LocalDate startDate, LocalDate endDate) {
        List<Map<String, Object>> allData = new ArrayList<>();

        if (dataIds == null || dataIds.isEmpty()) {
            log.warn("[HBase查询] 数据ID列表为空，跳过查询");
            return allData;
        }

        // 按月份分组查询（HBase表按月分表）
        Set<String> months = getMonthsBetween(startDate, endDate);
        log.info("[HBase查询] 协议: {} | 月份数: {} | 数据ID数: {}", protocolType, months.size(), dataIds.size());

        for (String month : months) {
            String tableName = hBaseQueryUtil.getTableName(protocolType, month);

            try {
                // 检查表是否存在
                if (!hBaseQueryUtil.tableExists(tableName)) {
                    log.warn("[HBase查询] 表不存在，跳过查询 | 表: {}", tableName);
                    continue;
                }

                log.debug("[HBase查询] 开始查询 | 表: {} | 数据ID数: {}", tableName, dataIds.size());
                List<Map<String, Object>> monthData = hBaseQueryUtil.batchGet(tableName, dataIds);
                allData.addAll(monthData);
                log.debug("[HBase查询] 查询完成 | 表: {} | 结果数: {}", tableName, monthData.size());

            } catch (Exception e) {
                log.error("[HBase查询] 查询失败 | 表: {} | 错误: {}", tableName, e.getMessage(), e);
                // 继续处理其他月份的数据，不中断整个流程
            }
        }

        log.info("[HBase查询] 总查询完成 | 协议: {} | 总结果数: {}", protocolType, allData.size());
        return allData;
    }

    /**
     * 获取时间范围内的月份列表
     */
    private Set<String> getMonthsBetween(LocalDate startDate, LocalDate endDate) {
        Set<String> months = new HashSet<>();
        LocalDate current = startDate.withDayOfMonth(1);

        while (!current.isAfter(endDate)) {
            months.add(current.format(DateTimeFormatter.ofPattern("yyyyMM")));
            current = current.plusMonths(1);
        }

        return months;
    }

    /**
     * 生成CSV文件
     */
    private void generateCsvFile(Integer metricId, String protocolType,
                               List<Map<String, Object>> hbaseData,
                               List<DetailFieldConfig> fieldConfigs) throws IOException {

        long timestamp = System.currentTimeMillis();
        Path filePath = fileSystemUtil.createExportFilePath(metricId, protocolType, timestamp);

        try (BufferedWriter writer = Files.newBufferedWriter(filePath, StandardCharsets.UTF_8)) {
            // 1. 写入CSV头部
            writeCsvHeader(writer, fieldConfigs);

            // 2. 写入数据行
            for (Map<String, Object> rowData : hbaseData) {
                writeCsvRow(writer, rowData, fieldConfigs, metricId, protocolType);
            }
        }

        log.info("[文件生成] CSV文件生成成功: {}", filePath.toString());
    }

    /**
     * 写入CSV头部
     */
    private void writeCsvHeader(BufferedWriter writer, List<DetailFieldConfig> fieldConfigs) throws IOException {
        List<String> headers = new ArrayList<>();

        // 通用字段
        headers.addAll(Arrays.asList("监控指标", "系统", "数据层级", "表名", "协议类型编码",  "指标账号"));

        // 协议明细字段
        for (DetailFieldConfig config : fieldConfigs) {
            headers.add(config.getFieldName());
        }

        writer.write(String.join(",", headers));
        writer.newLine();
    }

    /**
     * 写入CSV数据行
     */
    private void writeCsvRow(BufferedWriter writer, Map<String, Object> rowData,
                           List<DetailFieldConfig> fieldConfigs, Integer metricId, String protocolType) throws IOException {
        // 预处理：去除 "_data_" 前缀
        Map<String, Object> processedRowData = new HashMap<>();
        for (Map.Entry<String, Object> entry : rowData.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            //忽略es里的,保留hbase里的data_id
            if ("data_id".equals(key)){
                continue;
            }

            if (key.startsWith("_data_")) {
                // 去除 "_data_" 前缀
                key = key.substring("_data_".length());

            }
            processedRowData.put(key, value);
        }


        List<String> values = new ArrayList<>();

        // 通用字段值
        values.add(metricId.toString()); // 监控指标
        values.add("搜索"); // 系统
        values.add("HBase"); // 数据层级
        values.add("明细表"); // 表名
        values.add(protocolType); // 协议类型编码
        values.add(escapeCSV(String.valueOf(processedRowData.getOrDefault("account", "")))); // 指标账号

        // 协议明细字段值
        for (DetailFieldConfig config : fieldConfigs) {
            String fieldValue = String.valueOf(processedRowData.getOrDefault(config.getFieldName(), ""));
            values.add(escapeCSV(fieldValue));
        }

        writer.write(String.join(",", values));
        writer.newLine();
    }

    /**
     * CSV字段转义
     */
    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }

        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }

        return value;
    }
}
