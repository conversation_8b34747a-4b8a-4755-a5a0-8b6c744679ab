package com.semptian.service.impl.parser;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.semptian.common.SystemEnum;
import com.semptian.entity.BusinessApiConfig;
import com.semptian.entity.OpsApiAccessResult;
import com.semptian.service.MetricKvDataParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 指标10 综合搜索系统 KV数据解析接口
 *
 * <AUTHOR> Hu
 * @since 2025/5/22
 */
@Component
@Slf4j
public class MetricKvDataSpecial2Parser implements MetricKvDataParser {


    @Override
    public void parseData(List<BusinessApiConfig> apiConfigList, OpsApiAccessResult result, Map<String, Map<String, Integer>> systemKvMap) {
        //指标10的综合搜索kv数据, key是基站编号,value是次数。对比时只需要对比基站编号是否一致,不关心次数。
        String systemName = result.getSystemName();

        if (StrUtil.isEmpty(result.getKvContent())) {
            log.warn("System {}, account {}, statDate{}, has no  kvContent", result.getSystemName(), result.getAccount(), result.getStatDate());
            return;
        }

        try {
            // 解析kv_content字段
            JSONArray kvContentArray = JSONArray.parseArray(result.getKvContent());

            // 存储解析后的数据
            Map<String, Integer> parsedKvData = new HashMap<>();

            // 解析每个kv数据
            for (Object kvObj : kvContentArray) {
                JSONObject kvJson = (JSONObject) kvObj;

                for (String key : kvJson.keySet()) {
                    if (StrUtil.isNotEmpty(key)) {
                        //次数都填充为1
                        parsedKvData.put(key, 1);
                    }
                }
            }

            // 存储解析后的数据
            systemKvMap.put(result.getSystemName(), parsedKvData);
        }catch (Exception e) {
            log.error("System {},account {}, statDate{}, parse kv_content:{} error", systemName, result.getAccount(), result.getStatDate(), result.getKvContent(), e);
        }
    }

    @Override
    public List<Integer> getMetricIds() {
        return Lists.newArrayList(10);
    }

    @Override
    public List<String> getSystemNames() {
        return Lists.newArrayList(SystemEnum.SEARCH.getChineseName());
    }
}