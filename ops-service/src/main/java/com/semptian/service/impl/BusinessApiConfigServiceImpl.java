package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.annotation.OperateMock;
import com.semptian.entity.BusinessApiConfig;
import com.semptian.mapper.BusinessApiConfigMapper;
import com.semptian.service.BusinessApiConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class BusinessApiConfigServiceImpl extends ServiceImpl<BusinessApiConfigMapper, BusinessApiConfig> implements BusinessApiConfigService {

    @Override
    public Page<BusinessApiConfig> getApiConfigList(int pageNum, int pageSize, String systemName, String apiPath, String authType) {
        Page<BusinessApiConfig> page = new Page<>(pageNum, pageSize);
        QueryWrapper<BusinessApiConfig> wrapper = new QueryWrapper<>();
        if (systemName != null) wrapper.like("system_name", systemName);
        if (apiPath != null) wrapper.like("api_path", apiPath);
        if (authType != null) wrapper.eq("auth_type", authType);
        return baseMapper.selectPage(page, wrapper);
    }

    @OperateMock
    @Override
    public List<BusinessApiConfig> getApiConfigByMetricId(Integer metricId) {
        QueryWrapper<BusinessApiConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("metric_id", metricId);
        return baseMapper.selectList(wrapper);
    }

}