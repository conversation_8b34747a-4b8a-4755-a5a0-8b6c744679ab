package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.SpamCallNumberInfo;
import com.semptian.mapper.SpamCallNumberInfoMapper;
import com.semptian.service.SpamCallNumberInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 骚扰号码信息服务实现类
 */
@Slf4j
@Service
public class SpamCallNumberInfoServiceImpl extends ServiceImpl<SpamCallNumberInfoMapper, SpamCallNumberInfo> implements SpamCallNumberInfoService {

    @Resource
    private SpamCallNumberInfoMapper spamCallNumberInfoMapper;
    
    // 使用ConcurrentHashMap缓存骚扰号码，提高查询效率
    private final Set<String> spamNumbersCache = ConcurrentHashMap.newKeySet();
    
    // 上次缓存更新时间
    private long lastCacheUpdateTime = 0;
    
    // 缓存过期时间（10分钟）
    private static final long CACHE_EXPIRATION_TIME = 10 * 60 * 1000;

    @Override
    public List<SpamCallNumberInfo> getAllActiveSpamNumbers() {
        return spamCallNumberInfoMapper.getAllActiveSpamNumbers();
    }

    @Override
    public Set<String> getAllActiveSpamNumbersSet() {
        // 检查缓存是否过期
        long currentTime = System.currentTimeMillis();
        if (spamNumbersCache.isEmpty() || (currentTime - lastCacheUpdateTime) > CACHE_EXPIRATION_TIME) {
            synchronized (this) {
                // 双重检查锁定，避免多线程重复更新缓存
                if (spamNumbersCache.isEmpty() || (currentTime - lastCacheUpdateTime) > CACHE_EXPIRATION_TIME) {
                    updateCache();
                }
            }
        }
        return spamNumbersCache;
    }
    
    /**
     * 更新骚扰号码缓存
     */
    private void updateCache() {
        log.info("更新骚扰号码缓存");
        List<SpamCallNumberInfo> spamNumbers = getAllActiveSpamNumbers();
        
        // 清空现有缓存
        spamNumbersCache.clear();
        
        // 处理每个骚扰号码记录
        for (SpamCallNumberInfo info : spamNumbers) {
            // 处理可能包含多个号码的情况（逗号分隔）
            String[] numbers = info.getPhoneNumber().split(",");
            for (String number : numbers) {
                spamNumbersCache.add(number.trim());
            }
        }
        
        log.info("骚扰号码缓存更新完成，共 {} 个号码", spamNumbersCache.size());
        lastCacheUpdateTime = System.currentTimeMillis();
    }

    @Override
    public boolean isSpamNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }
        
        // 获取骚扰号码集合并检查
        return getAllActiveSpamNumbersSet().contains(phoneNumber.trim());
    }
}
