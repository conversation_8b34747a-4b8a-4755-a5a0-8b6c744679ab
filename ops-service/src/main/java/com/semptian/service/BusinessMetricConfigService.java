package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.BusinessMetricConfig;

public interface BusinessMetricConfigService extends IService<BusinessMetricConfig> {
    Page<BusinessMetricConfig> getMetricConfigList(int pageNum, int pageSize, String metricModelName, String metricName, String status);

    BusinessMetricConfig getByIdWithService(Integer metricId);
}