package com.semptian.service.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.semptian.model.vo.AdsOpsOpsApiAccessResult;
import com.semptian.service.SpamCallNumberInfoService;
import com.semptian.service.impl.InterSystemDataProducerServiceImpl;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 数组数据解析策略
 * 处理JSON数组类型的数据（typeValue=1）
 */
@Slf4j
public class ArrayDataParseStrategy implements ResponseParseStrategy {

    // 数字正则表达式模式
    private static final Pattern NUMERIC_PATTERN = Pattern.compile("^\\d+$");

    // 骚扰号码服务，由InterSystemDataProducerServiceImpl注入
    @Setter
    private static SpamCallNumberInfoService spamCallNumberInfoService;

    @Override
    public void parseResponse(JSONObject apiResponseJSONObject, JSONObject responseMapping,
                              String pathKey, AdsOpsOpsApiAccessResult resultData) {
        //--------------------------------1.解析出JSON数组-------------------------------
        // 1. 获取数据数组对象
        Object valueByPath = getValueByPath(apiResponseJSONObject, pathKey.split("\\."));

        // 2. 转换为JSON数组
        JSONArray dataArray = safeCastToJsonArray(valueByPath);

        // 如果没有解析出来，设置默认值0
        if (dataArray == null) {
            setDefaultResult(resultData);
            return;
        }
        //--------------------------------2.对JSON数组做处理-------------------------------
        // 1. 解析excludeKeys数组
        String[] excludeKeys = parseExcludeKeys(responseMapping);

        // 2. 解析excludeValues数组
        String[] excludeValues = parseExcludeValues(responseMapping);

        // 3. 解析条件,key一定是String类型,Value说不定,可能是String也可能是数值类型,故用Object
        List<Map<String, Object>> conditionKVs = parseConditionKVs(responseMapping);

        // 4. 解析countKey TODO 当前仅允许countKeys中仅有一个key,因为多个key的value累加起来没有意义
        //  TODO 但是还是使用数组,为将来可能需要有多个key各自对value求和提供可扩展性
        String[] countKeys = parseCountKey(responseMapping);

        // 5. 解析kvContentKeys 数组
        String[] kvContentKeys = parseKVContentKeys(responseMapping);

        //6. 解析 removeDuplicateStations 数组 过滤Value相同的JSON对象
        String[] removeDuplicateValueKeys = parseRemoveDuplicateStations(responseMapping);

        //7. 解析 isStationCount 表示是否开启基站类型的特殊计数方式
        Boolean isStationCount = responseMapping.getIntValue("isStationCount") != 0;
        //--------------------------------3.计算count-------------------------------
        // 8. 解析数据类型正则表达式
        String dataTypeRegex = responseMapping.getString("dataTypeRegex");

        // 9. 计算统计值
        long sum = calculateArraySum(dataArray, excludeKeys, excludeValues, conditionKVs,
                countKeys, removeDuplicateValueKeys, isStationCount, dataTypeRegex, responseMapping);

        // 检查是否需要进行跨系统数据比对和限制
        sum = checkAndHandleInterSystemDataComparison(responseMapping, resultData, sum);

        resultData.setStatCount(sum);
        //--------------------------------4.记录kv_content-------------------------------
        // 7. 根据is_kv判断是否需要记录kv
        resultData.setKv(responseMapping.getIntValue("is_kv") != 0);

        // 8. 构建kv_content的JSON内容
        if (resultData.isKv()) {
            String jsonContent = buildJsonArrayContent(dataArray, excludeKeys,
                    excludeValues, conditionKVs, kvContentKeys, removeDuplicateValueKeys, isStationCount, dataTypeRegex, responseMapping);
            resultData.setKvContent(jsonContent);
            logFormattedJson(jsonContent);
        } else {
            resultData.setKvContent("[]");
        }
    }

    private String[] parseRemoveDuplicateStations(JSONObject responseMapping) {
        JSONArray removeDuplicateValueKeysJSONArr = responseMapping.getJSONArray("removeDuplicateValueKeys");
        //如果在ResponseMapping中未配置removeDuplicateValueKeys，或者配置的是个空数组，那么就返回一个空数组
        if (removeDuplicateValueKeysJSONArr == null || removeDuplicateValueKeysJSONArr.isEmpty()) {
            return new String[0];
        }
        //如果配置了removeDuplicateValueKeys，那么就解析出来
        String[] results = new String[removeDuplicateValueKeysJSONArr.size()];
        for (int i = 0; i < removeDuplicateValueKeysJSONArr.size(); i++) {
            results[i] = removeDuplicateValueKeysJSONArr.getString(i);
        }
        return results;
    }

    /**
     * 解析kvContentKey数组
     *
     * @param responseMapping 响应映射配置
     * @return kvContentKey数组
     */
    private String[] parseKVContentKeys(JSONObject responseMapping) {
        JSONArray kvContentKeyJSONArr = responseMapping.getJSONArray("kvContentKey");
        //如果在ResponseMapping中未配置kvContentKey，或者配置的是个空数组，那么就返回一个空数组
        if (kvContentKeyJSONArr == null || kvContentKeyJSONArr.isEmpty()) {
            return new String[0];
        }
        //如果配置了kvContentKey，那么就解析出来
        String[] results = new String[kvContentKeyJSONArr.size()];
        for (int i = 0; i < kvContentKeyJSONArr.size(); i++) {
            results[i] = kvContentKeyJSONArr.getString(i);
        }
        return results;
    }

    private String[] parseExcludeValues(JSONObject responseMapping) {
        JSONArray excludeValueArray = responseMapping.getJSONArray("excludeValue");
        //如果在ResponseMapping中未配置excludeValue，或者配置的是个空数组，那么就返回一个空数组
        if (excludeValueArray == null || excludeValueArray.isEmpty()) {
            return new String[0];
        }
        //如果配置了excludeKey，那么就解析出来
        String[] results = new String[excludeValueArray.size()];
        for (int i = 0; i < excludeValueArray.size(); i++) {
            //TODO 当前excludeValue只支持String类型,后续可以改成支持多种,
            results[i] = excludeValueArray.getString(i);
        }
        return results;
    }

    /**
     * 根据路径获取JSON对象中的值
     *
     * @param apiResponseJSONObject JSON源对象
     * @param pathSegments          路径段数组 例如:["data","list"]
     * @return 获取到的值，如果路径无效则返回null
     */
    private Object getValueByPath(JSONObject apiResponseJSONObject, String[] pathSegments) {
        //之所以用Object,是因为有可能是JSON对象，也可能是JSON数组,也有可能是数值
        Object apiResponseJSONObjectTemp = apiResponseJSONObject;
        for (String segment : pathSegments) {
            if (!(apiResponseJSONObjectTemp instanceof JSONObject)) {
                String path = String.join(".", pathSegments);
                log.warn("JSON无法匹配结果解析格式,当前路径 {}, JSON是 {}", path, apiResponseJSONObject);
                return null;
            }
            apiResponseJSONObjectTemp = ((JSONObject) apiResponseJSONObjectTemp).get(segment);
        }
        return apiResponseJSONObjectTemp;
    }

    /**
     * 安全地将对象转换为JSON数组
     *
     * @param obj 要转换的对象
     * @return 转换后的JSON数组，如果转换失败则返回null
     */
    private JSONArray safeCastToJsonArray(Object obj) {
        if (!(obj instanceof JSONArray)) {
            log.warn("类型转换失败，不是JSON数组，当前对象是 {}", obj);
            return null;
        }
        return (JSONArray) obj;
    }

    /**
     * 设置默认结果
     *
     * @param resultData 结果数据对象
     */
    private void setDefaultResult(AdsOpsOpsApiAccessResult resultData) {
        log.warn("返回值JSON无法匹配结果解析格式,设置默认值为0");
        resultData.setStatCount(0);
        resultData.setKvContent("{}");
    }

    /**
     * 解析条件键
     *
     * @param responseMapping 响应映射配置
     * @return 条件列表
     */
    private List<Map<String, Object>> parseConditionKVs(JSONObject responseMapping) {
        JSONArray conditionKVJSONArr = responseMapping.getJSONArray("conditionKeyValue");
        //如果在ResponseMapping中未配置conditionKeyValue，或者配置的是个空数组，那么就返回一个空数组
        if (conditionKVJSONArr == null || conditionKVJSONArr.isEmpty()) {
            return new Vector<>();
        }
        //如果配置了conditionKeyValue，那么就解析出来
        List<Map<String, Object>> conditions = new Vector<>();

        for (int i = 0; i < conditionKVJSONArr.size(); i++) {
            JSONObject conditionObj = conditionKVJSONArr.getJSONObject(i);
            //忽略掉key是condition的JSON对象,因为这个条件用于判断and还是or,非过滤条件
//            if (conditionObj != null && !conditionObj.isEmpty() && conditionObj.containsKey("condition")) {
//                continue;
//            }TODO 要加入这个而不是忽略
            //设计上保证conditionObj中只有一个键
            String key = conditionObj.keySet().iterator().next();
            Map<String, Object> condition = new HashMap<>();
            condition.put("key", key);
            condition.put("value", conditionObj.get(key));
            conditions.add(condition);
        }
        return conditions;
    }

    /**
     * 解析count键
     *
     * @param responseMapping 响应映射配置
     * @return 目标键数组
     */
    private String[] parseCountKey(JSONObject responseMapping) {
        JSONArray countKeyJSONArr = responseMapping.getJSONArray("countKey");
        if (countKeyJSONArr == null || countKeyJSONArr.isEmpty()) {
            return new String[0];
        }
        //如果配置了countKey，那么就解析出来
        String[] results = new String[countKeyJSONArr.size()];
        for (int i = 0; i < countKeyJSONArr.size(); i++) {
            results[i] = countKeyJSONArr.getString(i);
        }
        return results;
    }

    /**
     * 解析排除键数组
     *
     * @param responseMapping 响应映射配置
     * @return 排除键数组
     */
    private String[] parseExcludeKeys(JSONObject responseMapping) {
        JSONArray excludeKeysArray = responseMapping.getJSONArray("excludeKey");
        //如果在ResponseMapping中未配置excludeKey，或者配置的是个空数组，那么就返回一个空数组
        if (excludeKeysArray == null || excludeKeysArray.isEmpty()) {
            return new String[0];
        }
        //如果配置了excludeKey，那么就解析出来
        String[] excludeKeys = new String[excludeKeysArray.size()];
        for (int i = 0; i < excludeKeysArray.size(); i++) {
            excludeKeys[i] = excludeKeysArray.getString(i);
        }
        return excludeKeys;
    }


    /**
     * 计算JSON数组中符合条件的元素的特定字段值的总和
     *
     * @param dataArray     JSON数组
     * @param excludeKeys   排除键数组
     * @param excludeValues 排除值数组
     * @param conditionKVs  条件键值对列表
     * @param countKeys     要计算的目标键数组
     * @return 计算得到的总和
     */
    private long calculateArraySum(JSONArray dataArray, String[] excludeKeys, String[] excludeValues,
                                   List<Map<String, Object>> conditionKVs, String[] countKeys,
                                   String[] removeDuplicateValueKeys, Boolean isStationCount, String dataTypeRegex,
                                   JSONObject responseMapping
    ) {
        // 1. 先根据excludeKeys过滤数组
        List<JSONObject> filteredByExcludeKeys = filterByExcludeKeys(dataArray, excludeKeys);

        // 2. 根据excludeValues过滤数组
        List<JSONObject> filteredByExcludeValues = filterByExcludeValues(filteredByExcludeKeys, excludeValues);

        // 3. 根据conditionKVs过滤数组
        List<JSONObject> filteredByConditions = filterByConditionKVs(filteredByExcludeValues, conditionKVs);

        // 4. 根据removeDuplicateValueKeys过滤数组
        List<JSONObject> filteredByRemoveDuplicateValueKeys = filterByRemoveDuplicateValueKeys(filteredByConditions, removeDuplicateValueKeys);

        // 5. 根据dataTypeRegex过滤数组
        List<JSONObject> filteredByDataTypeRegex = filterByDataTypeRegex(filteredByRemoveDuplicateValueKeys, dataTypeRegex);

        // 5.1 根据骚扰号码过滤数组
        List<JSONObject> filteredBySpamNumbers = filterBySpamNumbers(filteredByDataTypeRegex, responseMapping);

        // 6. 根据countKeys计算总和
        return calculateSum(filteredBySpamNumbers, countKeys, isStationCount);
    }

    /**
     * 根据指定键值去重 JSON 对象列表
     *
     * @param jsonObjects              要过滤的 JSON 对象列表
     * @param removeDuplicateValueKeys 用于去重的键名数组
     * @return 去重后的 JSON 对象列表
     */
    private List<JSONObject> filterByRemoveDuplicateValueKeys(List<JSONObject> jsonObjects
            , String[] removeDuplicateValueKeys) {
        // 如果没有配置 或者 配置的是空数组，说明无需满足任何条件,直接返回原列表
        if (removeDuplicateValueKeys == null || removeDuplicateValueKeys.length == 0) {
            return jsonObjects;
        }

        // 如果原始列表为空，直接返回空列表
        if (jsonObjects == null || jsonObjects.isEmpty()) {
            return new ArrayList<>();
        }

        // 初始化结果列表，初始包含所有原始对象
        List<JSONObject> result = new ArrayList<>(jsonObjects);

        // 逐个处理每个去重键
        for (String key : removeDuplicateValueKeys) {
            // 如果当前结果列表为空，直接返回空列表
            if (result.isEmpty()) {
                return result;
            }

            // 对当前结果列表按照当前键进行去重
            result = removeDuplicatesByKey(result, key);
        }

        return result;
    }

    /**
     * 根据指定键去除重复的 JSON 对象
     *
     * @param jsonObjects JSON 对象列表
     * @param key         用于去重的键名
     * @return 去重后的 JSON 对象列表
     */
    private List<JSONObject> removeDuplicatesByKey(List<JSONObject> jsonObjects, String key) {
        List<JSONObject> result = new ArrayList<>();
        Set<String> seenValues = new HashSet<>();

        for (JSONObject jsonObject : jsonObjects) {
            // 忽略大小写匹配键
            String actualKey = findKeyIgnoreCase(jsonObject, key);

            // 如果对象不包含该键，跳过该对象
            if (actualKey == null) {
                continue;
            }

            // 获取键对应的值
            Object value = jsonObject.get(actualKey);
            String valueStr = value == null ? "null" : String.valueOf(value);

            // 如果该值是第一次出现，则保留该对象
            if (!seenValues.contains(valueStr)) {
                seenValues.add(valueStr);
                result.add(jsonObject);
            }
        }

        return result;
    }

    /**
     * 在 JSON 对象中查找忽略大小写匹配的键
     *
     * @param jsonObject 要查找的 JSON 对象
     * @param targetKey  目标键名
     * @return 找到的实际键名，未找到返回 null
     */
    private String findKeyIgnoreCase(JSONObject jsonObject, String targetKey) {
        for (String key : jsonObject.keySet()) {
            if (key.equalsIgnoreCase(targetKey)) {
                return key;
            }
        }
        return null;
    }

    /**
     * 根据排除值过滤JSON对象列表
     *
     * @param jsonObjects   要过滤的JSON对象列表
     * @param excludeValues 排除值数组
     * @return 过滤后的JSON对象列表
     */
    private List<JSONObject> filterByExcludeValues(List<JSONObject> jsonObjects, String[] excludeValues) {
        // 如果没有配置excludeValues 或者 配置空数组，则直接返回原始列表
        if (excludeValues == null || excludeValues.length == 0) {
            return jsonObjects;
        }

        List<JSONObject> filteredList = new ArrayList<>();

        for (JSONObject jsonObject : jsonObjects) {
            boolean shouldExclude = false;

            // 检查JSON对象的所有键值对
            for (String key : jsonObject.keySet()) {
                //目前仅支持String!!!
                String valueStr = String.valueOf(jsonObject.get(key));

                // 检查值是否应该被排除
                if (shouldExcludeValue(valueStr, excludeValues)) {
                    shouldExclude = true;
                    break;
                }
            }

            // 如果不需要排除，添加到过滤后的列表中
            if (!shouldExclude) {
                filteredList.add(jsonObject);
            }
        }

        return filteredList;
    }

    /**
     * 根据数据类型正则表达式过滤JSON对象列表
     *
     * @param jsonObjects   要过滤的JSON对象列表
     * @param dataTypeRegex 数据类型正则表达式
     * @return 过滤后的JSON对象列表
     */
    private List<JSONObject> filterByDataTypeRegex(List<JSONObject> jsonObjects, String dataTypeRegex) {
        // 如果没有配置dataTypeRegex，则直接返回原始列表
        if (dataTypeRegex == null || dataTypeRegex.isEmpty()) {
            return jsonObjects;
        }

        List<JSONObject> filteredList = new ArrayList<>();

        for (JSONObject jsonObject : jsonObjects) {
            boolean hasValidDataType = false;

            // 检查JSON对象的所有键值对
            for (String key : jsonObject.keySet()) {
                if ("key".equals(key)){
                    String valueStr = jsonObject.getString(key);
                    // 检查值是否符合数据类型正则表达式
                    if (valueStr.matches(dataTypeRegex)) {
                        hasValidDataType = true;
                        break;
                    }
                }
            }

            // 如果有符合数据类型的值，添加到过滤后的列表中
            if (hasValidDataType) {
                filteredList.add(jsonObject);
            }
        }

        return filteredList;
    }

    /**
     * 根据骚扰号码过滤JSON对象列表
     *
     * @param jsonObjects     要过滤的JSON对象列表
     * @param responseMapping 响应映射配置
     * @return 过滤后的JSON对象列表
     */
    private List<JSONObject> filterBySpamNumbers(List<JSONObject> jsonObjects, JSONObject responseMapping) {
        // 如果没有配置spamNumberFilter或者骚扰号码服务未初始化，则直接返回原始列表
        if (responseMapping == null || !responseMapping.containsKey("spamNumberFilter") ||
                responseMapping.getIntValue("spamNumberFilter") != 1 || spamCallNumberInfoService == null) {
            return jsonObjects;
        }

        log.debug("开始进行骚扰号码过滤，原始数据量: {}", jsonObjects.size());

        List<JSONObject> filteredList = new ArrayList<>();

        for (JSONObject jsonObject : jsonObjects) {
            boolean isNormalNumber = true;

            // 查找电话号码字段（忽略大小写）
            for (String key : jsonObject.keySet()) {
                if ("key".equals(key)){
                    String valueStr = jsonObject.getString(key);
                    // 检查值是否符合数据类型正则表达式
                    if (spamCallNumberInfoService.isSpamNumber(valueStr)) {
                        isNormalNumber = false;
                        break;
                    }
                }
            }

            // 如果是正常号码，添加到过滤后的列表中
            if (isNormalNumber) {
                filteredList.add(jsonObject);
            }
        }

        log.debug("骚扰号码过滤完成，过滤后数据量: {}, 过滤掉 {} 条骚扰号码",
                filteredList.size(), jsonObjects.size() - filteredList.size());

        return filteredList;
    }

    /**
     * 判断值是否应该被排除
     *
     * @param value         要检查的值
     * @param excludeValues 排除值数组
     * @return 如果应该排除返回true，否则返回false
     */
    private boolean shouldExcludeValue(String value, String[] excludeValues) {
        if (excludeValues == null || excludeValues.length == 0) {
            return false;
        }

        // 检查值是否与任何排除值匹配（忽略大小写）
        for (String excludeValue : excludeValues) {
            if (value.equalsIgnoreCase(excludeValue)) {
                return true;
            }
        }

        return false;
    }


    /**
     * 根据排除键过滤JSON数组
     *
     * @param dataArray   要过滤的JSON数组
     * @param excludeKeys 排除键数组
     * @return 过滤后的JSON对象列表
     */
    private List<JSONObject> filterByExcludeKeys(JSONArray dataArray, String[] excludeKeys) {
        List<JSONObject> filteredList = new ArrayList<>();

        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject jsonObject = dataArray.getJSONObject(i);
            // 如果不需要排除，添加到过滤后的列表中
            if (!shouldExclude(jsonObject, excludeKeys)) {
                filteredList.add(jsonObject);
            }
        }

        return filteredList;
    }

    /**
     * 根据条件过滤JSON对象列表
     *
     * @param jsonObjects  要过滤的JSON对象列表
     * @param conditionKVs 条件列表
     * @return 过滤后的JSON对象列表
     */
    private List<JSONObject> filterByConditionKVs(List<JSONObject> jsonObjects, List<Map<String, Object>> conditionKVs) {
        // 如果没有配置 或者 配置的是空数组，说明无需满足任何条件,直接返回原列表
        if (conditionKVs == null || conditionKVs.isEmpty()) {
            return jsonObjects;
        }
        //走到这,说明conditionKVs里面有条件,需要遍历查看是否满足
        List<JSONObject> filteredList = new ArrayList<>();

        for (JSONObject jsonObject : jsonObjects) {
            // 判断该对象是否满足条件,如果满足条件，添加到过滤后的列表中
            if (isJSONObjectValid(jsonObject, conditionKVs)) {
                filteredList.add(jsonObject);
            }
        }

        return filteredList;
    }

    /**
     * 计算JSON对象列表中的数值总和
     *
     * @param jsonObjects JSON对象列表
     * @param countKeys   要计算的目标键数组
     * @return 计算得到的总和
     */
    private long calculateSum(List<JSONObject> jsonObjects, String[] countKeys, Boolean isStationCount) {
        // 如果isStationCount为true，则直接返回jsonObjects.size()作为总和,因为已经经过去重了
        if (isStationCount) {
            return removebaseStationNoEmptyAndSum(jsonObjects);
        }

        long sum = 0;
        for (JSONObject jsonObject : jsonObjects) {
            sum += sumObjectValue(jsonObject, countKeys);
        }
        return sum;
    }

    private long removebaseStationNoEmptyAndSum(List<JSONObject> jsonObjects) {
        ArrayList<JSONObject> results = new ArrayList<>();
        for (JSONObject jsonObject : jsonObjects) {
            if (!"".equals(jsonObject.getString("baseStationNo"))) {
                results.add(jsonObject);
            }
        }
        return results.size();
    }

    /**
     * 判断JSON对象是否应该被排除
     *
     * @param item        JSON对象
     * @param excludeKeys 排除键数组
     * @return 如果应该排除返回true，否则返回false
     */
    private boolean shouldExclude(JSONObject item, String[] excludeKeys) {
        //如果配置excludeKeys 或者 配了个空的 如果配置excludeKeys 那么该JSON对象不应该被排除
        if (excludeKeys == null || excludeKeys.length == 0) {
            return false;
        }
        //如果配置了excludeKeys,那么就要遍历该JSON对象所有key
        for (String key : item.keySet()) {
            //如果excludeKey与JSON对象的key匹配，说明该JSON需要被排除,则返回true
            for (String excludeKey : excludeKeys) {
                if (key.equalsIgnoreCase(excludeKey)) {
                    return true;
                }
            }
        }
        //如果没有匹配上excludeKey,说明该JSON不需要被排除,返回false
        return false;
    }

    /**
     * 判断JSON对象是否满足条件
     *
     * @param jsonObject JSON对象
     * @param conditions 条件列表
     * @return 如果满足条件返回true，否则返回false
     */
    private boolean isJSONObjectValid(JSONObject jsonObject, List<Map<String, Object>> conditions) {
        // 1. 如果没有条件，直接返回true
        if (conditions == null || conditions.isEmpty()) {
            return true;
        }

        // 2. 确定条件类型："and"或"or"
        String conditionType = getConditionType(conditions);
        boolean isAndCondition = "and".equalsIgnoreCase(conditionType);

        // 3. 处理不同类型的条件
        if (isAndCondition) {
            // AND条件：所有条件都必须满足
            return checkAllConditionsMatch(jsonObject, conditions);
        } else {
            // OR条件：只需满足一个条件
            return checkAnyConditionMatches(jsonObject, conditions);
        }
    }

    /**
     * 获取条件类型（"and"或"or"）
     *
     * @param conditions 条件列表
     * @return 条件类型，默认为"and"
     */
    private String getConditionType(List<Map<String, Object>> conditions) {
        for (Map<String, Object> condition : conditions) {
            if ("condition".equals(condition.get("key"))) {
                Object value = condition.get("value");
                if (value instanceof String) {
                    return (String) value;
                }
            }
        }
        //执行到这说明没有condition这个Key,说明配置有问题,要抛异常
        log.info("response_mapping中的conditionKeyValue没有配置{\"condition\":\"and\"},设置默认值and,请检查数据库配置!!!");
        return "and"; // 默认为"and"
    }

    /**
     * 检查是否所有条件都匹配（AND逻辑）
     *
     * @param jsonObject JSON对象
     * @param conditions 条件列表
     * @return 如果所有条件都匹配返回true，否则返回false
     */
    private boolean checkAllConditionsMatch(JSONObject jsonObject, List<Map<String, Object>> conditions) {
        for (Map<String, Object> condition : conditions) {
            // 跳过condition类型设置
            if (condition.containsKey("condition")) {
                continue;
            }

            // 检查当前条件是否匹配
            if (!isConditionMatched(jsonObject, condition)) {
                return false; // 只要有一个条件不匹配，就返回false
            }
        }

        return true; // 所有条件都匹配
    }

    /**
     * 检查是否有任一条件匹配（OR逻辑）
     *
     * @param jsonObject JSON对象
     * @param conditions 条件列表
     * @return 如果有任一条件匹配返回true，否则返回false
     */
    private boolean checkAnyConditionMatches(JSONObject jsonObject, List<Map<String, Object>> conditions) {
        for (Map<String, Object> condition : conditions) {
            // 跳过condition类型设置
            if ("condition".equals(condition.get("key"))) {
                continue;
            }

            // 检查当前条件是否匹配
            if (isConditionMatched(jsonObject, condition)) {
                return true; // 只要有一个条件匹配，就返回true
            }
        }

        return false; // 没有任何条件匹配
    }

    /**
     * 检查单个条件是否匹配
     *
     * @param jsonObject JSON对象
     * @param condition  条件
     * @return 如果条件匹配返回true，否则返回false
     */
    private boolean isConditionMatched(JSONObject jsonObject, Map<String, Object> condition) {
        String key = (String) condition.get("key");
        Object expectedValue = condition.get("value");

        // 如果key或value为空，则该条件无效
        if (key == null || expectedValue == null) {
            return false;
        }

        // 检查JSON对象是否包含该键
        if (!jsonObject.containsKey(key)) {
            return false;
        }

        // 获取实际值并进行比较
        Object actualValue = jsonObject.get(key);
        String expectedValueStr = String.valueOf(expectedValue);
        String actualValueStr = String.valueOf(actualValue);

        // 忽略大小写比较,相等则说明匹配
        return actualValueStr.equalsIgnoreCase(expectedValueStr);
    }

    /**
     * 计算JSON对象中指定键的数值总和
     *
     * @param jsonObject JSON对象
     * @param countKeys  要计算的目标键数组
     * @return 计算得到的总和
     */
    private long sumObjectValue(JSONObject jsonObject, String[] countKeys) {
        // 1. 参数检查
        if (jsonObject == null) {
            return 0;
        }

        // 2. 处理countKeys为空的情况
        if (countKeys == null || countKeys.length == 0) {
            // 遇到第一个数值类型的值就返回
            for (String key : jsonObject.keySet()) {
                Object value = jsonObject.get(key);
                if (value instanceof Number) {
                    return ((Number) value).longValue();
                }
            }
            // 如果没有找到数值类型的值，返回0
            return 0;
        } else {
            // 3. 处理countKeys不为空的情况
            // 取第一个元素作为目标键名
            String targetKey = countKeys[0];

            // 遇到第一个匹配的键就返回其值
            for (String key : jsonObject.keySet()) {
                // 使用equalsIgnoreCase进行大小写不敏感的比较
                if (key.equalsIgnoreCase(targetKey)) {
                    Object value = jsonObject.get(key);
                    if (value instanceof Number) {
                        return ((Number) value).longValue();
                    }
                }
            }
            // 如果没有找到匹配的键，返回0
            return 0;
        }
    }

    /**
     * 判断键是否应该被排除
     *
     * @param key         要检查的键
     * @param excludeKeys 排除键数组
     * @return 如果应该排除返回true，否则返回false
     */
    private boolean shouldExcludeKey(String key, String[] excludeKeys) {
        if (excludeKeys == null || excludeKeys.length == 0) {
            return false;
        }

        for (String excludeKey : excludeKeys) {
            if (key.equalsIgnoreCase(excludeKey)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 构建JSON数组内容
     *
     * @param dataArray     JSON数组
     * @param excludeKeys   排除键数组
     * @param excludeValues 排除值数组
     * @param conditionKVs  条件键值对列表
     * @param kvContentKeys 用于筛选参与构建的键值对
     * @return 构建的JSON数组字符串
     */
    private String buildJsonArrayContent(JSONArray dataArray, String[] excludeKeys,
                                         String[] excludeValues, List<Map<String, Object>> conditionKVs,
                                         String[] kvContentKeys, String[] removeDuplicateValueKeys, Boolean isStationCount,
                                         String dataTypeRegex, JSONObject responseMapping) {
        // 1. 先根据excludeKeys过滤数组
        List<JSONObject> filteredByExcludeKeys = filterByExcludeKeys(dataArray, excludeKeys);

        // 2. 根据excludeValues过滤数组
        List<JSONObject> filteredByExcludeValues = filterByExcludeValues(filteredByExcludeKeys, excludeValues);

        // 3. 再根据conditionKVs过滤数组
        List<JSONObject> filteredByConditions = filterByConditionKVs(filteredByExcludeValues, conditionKVs);

        // 4. 根据removeDuplicateValueKeys过滤数组
        List<JSONObject> filteredByRemoveDuplicateValueKeys = filterByRemoveDuplicateValueKeys(filteredByConditions, removeDuplicateValueKeys);

        // 5. 根据dataTypeRegex过滤数组
        List<JSONObject> filteredByDataTypeRegex = filterByDataTypeRegex(filteredByRemoveDuplicateValueKeys, dataTypeRegex);

        // 5.1 根据骚扰号码过滤数组
        List<JSONObject> filteredBySpamNumbers = filterBySpamNumbers(filteredByDataTypeRegex, responseMapping);

        // 6. 根据kvContentKeys构建JSON数组字符串
        return buildJsonArrayStringWithKeys(filteredBySpamNumbers, kvContentKeys, isStationCount);
    }

    /**
     * 根据指定的键构建JSON数组字符串
     *
     * @param jsonObjects   JSON对象列表
     * @param kvContentKeys 用于筛选参与构建的键值对
     * @return 构建的JSON数组字符串
     */
    private String buildJsonArrayStringWithKeys(List<JSONObject> jsonObjects, String[] kvContentKeys, Boolean isStationCount) {
        StringBuilder sb = new StringBuilder("[");

        for (int i = 0; i < jsonObjects.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(buildJsonObjectString(jsonObjects.get(i), kvContentKeys));
        }

        sb.append("]");
        if (isStationCount) {
            return deleteEmptyStationValue(sb.toString());
        }
        return sb.toString();
    }

    private String deleteEmptyStationValue(String jsonStr) {
        if (jsonStr.isEmpty()) {
            return jsonStr;
        }

        JSONArray jsonArray = JSON.parseArray(jsonStr);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if ("".equals(jsonObject.getString("baseStationNo"))) {
                jsonArray.remove(i);
                i--;
            }
        }

        return jsonArray.toJSONString();
    }

    /**
     * 根据指定的键构建JSON对象字符串
     *
     * @param jsonObject    JSON对象
     * @param kvContentKeys 用于筛选参与构建的键值对
     * @return 构建的JSON对象字符串
     */
    private String buildJsonObjectString(JSONObject jsonObject, String[] kvContentKeys) {
        StringBuilder sb = new StringBuilder("{");
        int count = 0;

        // 如果kvContentKeys未配置 或 配置的空数组，则添加所有键值对
        if (kvContentKeys == null || kvContentKeys.length == 0) {
            for (String key : jsonObject.keySet()) {
                if (count++ > 0) {
                    sb.append(",");
                }
                appendKeyValuePair(sb, key, jsonObject.get(key));
            }
        }
        // 如果kvContentKeys不为空，则只添加指定的键值对
        else {
            for (String contentKey : kvContentKeys) {
                String trimmedKey = contentKey.trim();

                // 忽略大小写匹配键
                for (String jsonKey : jsonObject.keySet()) {
                    if (jsonKey.equalsIgnoreCase(trimmedKey)) {
                        if (count++ > 0) {
                            sb.append(",");
                        }
                        appendKeyValuePair(sb, contentKey, jsonObject.get(jsonKey));
                        break; // 找到匹配的键后退出循环
                    }
                }
            }
        }

        sb.append("}");
        return sb.toString();
    }

    /**
     * 添加键值对到StringBuilder
     *
     * @param sb    StringBuilder对象
     * @param key   键
     * @param value 值
     */
    private void appendKeyValuePair(StringBuilder sb, String key, Object value) {
        // 处理不同类型的值
        if (value == null) {
            sb.append(String.format("\"%s\":null", key));
        } else if (value instanceof Number || value instanceof Boolean) {
            // 数字和布尔值不需要引号
            sb.append(String.format("\"%s\":%s", key, value));
        } else if (value instanceof JSONObject || value instanceof JSONArray) {
            // JSON对象或数组直接输出
            sb.append(String.format("\"%s\":%s", key, value));
        } else {
            // 其他类型作为字符串处理，需要转义字符串中的特殊字符
            String escapedValue = String.valueOf(value)
                    .replace("\\", "\\\\")
                    .replace("\"", "\\\"")
                    .replace("\n", "\\n")
                    .replace("\r", "\\r")
                    .replace("\t", "\\t");
            sb.append(String.format("\"%s\":\"%s\"", key, escapedValue));
        }
    }

    /**
     * 构建JSON数组字符串
     *
     * @param items           JSON对象列表
     * @param targetKeys      目标键数组
     * @param responseMapping 响应映射配置
     * @return 构建的JSON数组字符串
     */
    private String buildJsonArrayString(List<JSONObject> items, String[] targetKeys, JSONObject responseMapping) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < items.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(buildItemString(items.get(i), targetKeys, responseMapping));
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 构建单个JSON对象字符串
     *
     * @param item            JSON对象
     * @param targetKeys      目标键数组
     * @param responseMapping 响应映射配置
     * @return 构建的JSON对象字符串
     */
    private String buildItemString(JSONObject item, String[] targetKeys, JSONObject responseMapping) {
        StringBuilder sb = new StringBuilder("{");
        if (targetKeys.length == 0) {
            appendAllKeys(item, sb, responseMapping);
        } else {
            appendSpecificKeys(item, targetKeys, sb, responseMapping);
        }
        sb.append("}");
        return sb.toString();
    }

    /**
     * 添加所有键值对
     *
     * @param item            JSON对象
     * @param sb              字符串构建器
     * @param responseMapping 响应映射配置
     */
    private void appendAllKeys(JSONObject item, StringBuilder sb, JSONObject responseMapping) {
        String[] excludeKeys = parseExcludeKeys(responseMapping);
        int count = 0;

        for (String key : item.keySet()) {
            if (shouldExcludeKey(key, excludeKeys)) {
                continue;
            }

            if (count++ > 0) {
                sb.append(",");
            }
            sb.append(String.format("\"%s\":\"%s\"", key.toLowerCase(), item.get(key)));
        }
    }

    /**
     * 添加指定键值对
     *
     * @param item            JSON对象
     * @param targetKeys      目标键数组
     * @param sb              字符串构建器
     * @param responseMapping 响应映射配置
     */
    private void appendSpecificKeys(JSONObject item, String[] targetKeys, StringBuilder sb, JSONObject responseMapping) {
        String[] excludeKeys = parseExcludeKeys(responseMapping);
        int count = 0;

        for (String key : targetKeys) {
            // 检查当前键是否应该被排除
            if (shouldExcludeKey(key, excludeKeys)) {
                continue;
            }

            String actualKey = null;
            String trimmedKey = key.trim();

            // 忽略大小写匹配键
            for (String itemKey : item.keySet()) {
                if (itemKey.equalsIgnoreCase(trimmedKey)) {
                    actualKey = itemKey;
                    break;
                }
            }

            if (actualKey != null) {
                if (count++ > 0) {
                    sb.append(",");
                }
                sb.append(String.format("\"%s\":\"%s\"", key.toLowerCase(), item.get(actualKey)));
            }
        }
    }

    /**
     * 检查并处理跨系统数据比对和限制
     * <p>
     * 该方法检查是否需要进行跨系统数据比对，如果需要，则从ARCHIVE系统获取对应的结果，
     * 并根据比对规则调整统计值。当SEARCH系统的值大于ARCHIVE系统的值时，使用ARCHIVE系统的值。
     *
     * @param responseMapping 响应映射配置
     * @param resultData      结果数据对象
     * @param currentSum      当前计算的统计值
     * @return 调整后的统计值
     */
    private long checkAndHandleInterSystemDataComparison(JSONObject responseMapping,
                                                         AdsOpsOpsApiAccessResult resultData,
                                                         long currentSum) {
        //TODO 暂时没有用到,不影响代码正确性,只需要不配置 doArchiveFirst: 1 即可
        if (responseMapping.containsKey("doArchiveFirst") && responseMapping.getIntValue("doArchiveFirst") == 1
                && "综合搜索".equalsIgnoreCase(resultData.getSystemName())) {
            // 获取当前指标ID
            Integer metricId = Integer.parseInt(resultData.getMetricId());
            String account = resultData.getAccount();

            // 从 InterSystemDataProducerServiceImpl 类中获取 ARCHIVE 系统的结果
            try {
                // 获取 archiveResultsMap 字段
                Field archiveResultsMapField = InterSystemDataProducerServiceImpl.class.getDeclaredField("ARCHIVE_RESULTS_MAP");
                archiveResultsMapField.setAccessible(true);

                // 获取 archiveResultsMap 实例
                @SuppressWarnings("unchecked")
                ConcurrentHashMap<Long, Vector<AdsOpsOpsApiAccessResult>> archiveResultsMap =
                        (ConcurrentHashMap<Long, Vector<AdsOpsOpsApiAccessResult>>) archiveResultsMapField.get(null);

                // 获取当前指标的 ARCHIVE 系统结果
                Long metricIdLong = Long.parseLong(String.valueOf(metricId));
                if (archiveResultsMap.containsKey(metricIdLong)) {
                    Vector<AdsOpsOpsApiAccessResult> archiveResults = archiveResultsMap.get(metricIdLong);

                    // 查找匹配的 ARCHIVE 系统结果
                    for (AdsOpsOpsApiAccessResult archiveResult : archiveResults) {
                        if (account.equals(archiveResult.getAccount())) {
                            // 如果 SEARCH 系统的 sum 值大于 ARCHIVE 系统的 sum 值，则使用 ARCHIVE 系统的 sum 值
                            if (currentSum > archiveResult.getStatCount()) {
                                log.info("[跨系统数据比对] 指标ID: {} | 账号: {} | SEARCH 系统的 sum 值({}) 大于 ARCHIVE 系统的 sum 值({})，使用 ARCHIVE 系统的 sum 值",
                                        metricId, account, currentSum, archiveResult.getStatCount());
                                return archiveResult.getStatCount();
                            }
                            log.info("[跨系统数据比对] 指标ID: {} | 账号: {} | SEARCH 系统的 sum 值({}) 小于或等于 ARCHIVE 系统的 sum 值({})，使用 SEARCH 系统的 sum 值",
                                    metricId, account, currentSum, archiveResult.getStatCount());
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("[跨系统数据比对] 获取 ARCHIVE 系统结果失败: {}", e.getMessage(), e);
            }
        }
        return currentSum;
    }

    /**
     * 记录格式化的JSON
     *
     * @param jsonString JSON字符串
     */
    private void logFormattedJson(String jsonString) {
        if (log.isDebugEnabled()) {
            JSONArray jsonArray = JSON.parseArray(jsonString);
            log.debug("[KV内容] JSON数组: \n{}", JSON.toJSONString(jsonArray, true));
        }
    }
}
