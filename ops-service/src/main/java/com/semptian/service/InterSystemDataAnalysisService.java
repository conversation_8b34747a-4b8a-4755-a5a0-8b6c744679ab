package com.semptian.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.model.metrics.DailyTrendItem;
import com.semptian.model.metrics.DetailItem;
import com.semptian.model.metrics.MetricResponse;

import java.time.LocalDate;
import java.util.List;

public interface InterSystemDataAnalysisService {
    List<MetricResponse> getMetrics(int page, int size, String type, LocalDate startDate, LocalDate endDate);

    List<DailyTrendItem> getDailyTrend(String metricId, LocalDate startDate, LocalDate endDate);

    Page<DetailItem> getDetails(String metricId, int page, int size, String account, String sort,boolean desc, LocalDate startDate, LocalDate endDate);

    Object getHomepagePanel(LocalDate startDate, LocalDate endDate, int panelId);
}