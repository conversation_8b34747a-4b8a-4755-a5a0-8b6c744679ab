package com.semptian.aspect;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Mock数据加载器
 *
 * <AUTHOR>
 * &#064;date  2025/2/20 下午12:27
 */
public class MockDataLoader {
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final Path MOCK_ROOT = Paths.get("ops-service", "src", "test", "java", "mockfiles");

    /**
     * 加载Mock数据（自动匹配参数）
     *
     * @param targetClass 目标Service类
     * @param methodName  方法名称
     * @param params      方法参数
     * @return 匹配的返回结果
     */
    public static <T> T loadMock(Class<?> targetClass, String methodName, Object... params) {
        try {
            List<Map<String, Object>> records = readMockRecords(targetClass);

            return records.stream()
                .filter(record -> methodName.equals(record.get("method")))
                .filter(record -> matchParameters(record.get("args"), params))
                .findFirst()
                .map(record -> {
                    Object returnValue = record.get("result");
                    // 使用TypeReference来保留泛型信息
                    return mapper.convertValue(returnValue, new TypeReference<T>() {});
                })
                .orElseThrow(() -> new RuntimeException("未找到匹配的Mock数据"));
        } catch (Exception e) {
            throw new MockDataException("加载Mock数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 读取类对应的所有Mock记录
     */
    private static List<Map<String, Object>> readMockRecords(Class<?> targetClass) throws IOException {
        String fileName = targetClass.getSimpleName();
        Path filePath = MOCK_ROOT.resolve(fileName);

        if (!Files.exists(filePath)) {
            throw new MockDataException("Mock文件不存在: " + filePath, null);
        }

        return Files.readAllLines(filePath).stream().map(MockDataLoader::parseJsonLine).filter(Optional::isPresent).map(Optional::get).collect(java.util.stream.Collectors.toList());

    }

    /**
     * 参数匹配策略（按JSON序列化比较）
     */
    private static boolean matchParameters(Object recordArgs, Object[] inputParams) {
        try {
            String inputJson = mapper.writeValueAsString(inputParams);
            String recordJson = mapper.writeValueAsString(recordArgs);
            return inputJson.equals(recordJson);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 解析单行JSON记录
     */
    private static Optional<Map<String, Object>> parseJsonLine(String jsonLine) {
        try {
            return Optional.of(mapper.readValue(jsonLine, new TypeReference<Map<String, Object>>() {
            }));
        } catch (Exception e) {
            System.err.println("解析JSON行失败: " + jsonLine);
            return Optional.empty();
        }
    }

    public static class MockDataException extends RuntimeException {
        public MockDataException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}