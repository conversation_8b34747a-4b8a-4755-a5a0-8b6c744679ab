package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("tb_ops_business_sample_config")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessSampleConfig {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long metricId;
    private BusinessAccountConfig.AccountType accountType;
    private Integer sampleWeight = 100;
    private String dynamicSql;
    private Boolean enable = true;
    private Date lastSampleTime;
    private Integer sampleInterval = 1440;
    private Long createTime;
    private Long modifyTime;
}