{"args": [["综合搜索", "全息档案", "案件管理"], 9], "result": [{"id": 1, "sysName": "综合搜索", "sysType": "SEARCH", "env": "PROD", "baseUrl": "http://192.168.80.158:8888/search", "authType": "BASIC", "authConfig": "{\"account\": \"search_1\", \"auth_url\": \"http://192.168.80.158:8104/portal/singleLogin/login.json\", \"password\": \"39d9315cbd989fec964b3e7e91b88a68\", \"backLogin\": true}", "timeout": 5000, "enable": true, "appid": "23"}, {"id": 2, "sysName": "全息档案", "sysType": "ARCHIVE", "env": "PROD", "baseUrl": "http://192.168.80.158:8109/archives_web", "authType": "BASIC", "authConfig": "{\"account\": \"archive_1\", \"auth_url\": \"http://192.168.80.158:8104/portal/singleLogin/login.json\", \"password\": \"cc0f2fea0ccf6c6719d58f5078167bbf\", \"backLogin\": true}", "timeout": 5000, "enable": true, "appid": "92"}, {"id": 3, "sysName": "案件管理", "sysType": "CASE", "env": "PROD", "baseUrl": "http://192.168.80.158:8105/case-system", "authType": "BASIC", "authConfig": "{\"account\": \"guolijun_1\", \"auth_url\": \"http://192.168.80.158:8104/portal/singleLogin/login.json\", \"password\": \"fbec0584d547c9b61823eef5aa7f03d2\", \"backLogin\": true}", "timeout": 5000, "enable": true, "appid": "75"}], "method": "getSysConfigBySysNameAndMetricId", "class": "BusinessSysConfigServiceImpl", "timestamp": "2025-04-15T21:08:18.991", "argsType": ["[Ljava.lang.String;", "java.lang.Integer"]}