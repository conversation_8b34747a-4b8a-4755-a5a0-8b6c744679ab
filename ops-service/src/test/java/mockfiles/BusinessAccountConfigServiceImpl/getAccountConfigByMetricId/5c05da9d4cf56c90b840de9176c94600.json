{"args": [{"id": 7, "metricModelName": "号码数据差异分析", "metricName": "通信次数", "compareSystems": "综合搜索,全息档案,案件管理", "description": "某个号码账号特定时间范围内的通话、传真、短信总次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643123594883073, "metricId": 7, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883074, "metricId": 7, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883075, "metricId": 7, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883076, "metricId": 7, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883077, "metricId": 7, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883078, "metricId": 7, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883079, "metricId": 7, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883080, "metricId": 7, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883081, "metricId": 7, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643123594883082, "metricId": 7, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "9", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:45.371", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}