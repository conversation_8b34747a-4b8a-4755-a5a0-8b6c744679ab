{"args": [{"id": 12, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "关联虚拟账号数量", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内某个Fixed RADIUS账号在LIS协议中关联到的虚拟账号", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643129475297282, "metricId": 12, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297283, "metricId": 12, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297284, "metricId": 12, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297285, "metricId": 12, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297286, "metricId": 12, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297287, "metricId": 12, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297288, "metricId": 12, "accountType": "RADIUS", "accountValue": "0026954274p", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297289, "metricId": 12, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297290, "metricId": 12, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643129475297291, "metricId": 12, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "13", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:02:01.892", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}