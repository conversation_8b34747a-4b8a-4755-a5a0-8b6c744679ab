{"args": [{"id": 14, "metricModelName": "IM账号数据差异分析", "metricName": "通联账号及次数", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内，与IM账号进行即时聊天的其他IM账号以及聊天次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643131819913218, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913219, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913220, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913221, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913222, "metricId": 14, "accountType": "IM", "accountValue": "*********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913223, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913224, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913225, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913226, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643131819913227, "metricId": 14, "accountType": "IM", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "18", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:02:10.299", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}