{"args": [{"id": 17, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "认证记录", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内，某个Fixed RADIUS账号的认证记录", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643133782847489, "metricId": 17, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847490, "metricId": 17, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847491, "metricId": 17, "accountType": "RADIUS", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847492, "metricId": 17, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847493, "metricId": 17, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847494, "metricId": 17, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847495, "metricId": 17, "accountType": "RADIUS", "accountValue": "0026954274p", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847496, "metricId": 17, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847497, "metricId": 17, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643133782847498, "metricId": 17, "accountType": "RADIUS", "accountValue": "**********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "17", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:02:17.743", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}