{"args": [{"id": 16, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "上网明细", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内，某个Fixed RADIUS账号的上网LIS明细", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:02:13.145", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}