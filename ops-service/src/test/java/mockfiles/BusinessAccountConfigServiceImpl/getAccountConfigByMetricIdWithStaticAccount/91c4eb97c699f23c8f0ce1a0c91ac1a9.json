{"args": [{"id": 6, "metricModelName": "号码数据差异分析", "metricName": "上网关联虚拟账号", "compareSystems": "综合搜索,全息档案", "description": "某个号码账号特定时间范围内作为认证账号在LIS数据中出现，关联到的虚拟账号", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:40.291", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}