{"args": [{"id": 12, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "关联虚拟账号数量", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内某个Fixed RADIUS账号在LIS协议中关联到的虚拟账号", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1902683268504031238, "metricId": 12, "accountType": "RADIUS", "accountValue": "Gljhomeradius", "sampleWeight": 100, "sampleStrategy": "FIXED", "caseParam": null, "dynamic_condition_id": null, "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:02:01.899", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}