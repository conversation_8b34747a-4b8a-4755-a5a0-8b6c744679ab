{"args": [{"id": 3, "metricModelName": "Email数据差异分析", "metricName": "关联RADIUS账号数量", "compareSystems": "综合搜索,全息档案,案件管理", "description": "Email账号进行上网行为时关联的RADIUS账号，包括fixed radius、mobile radius、fixed ip", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1901935740991504396, "metricId": 3, "accountType": "EMAIL", "accountValue": "<EMAIL>", "sampleWeight": 100, "sampleStrategy": "FIXED", "caseParam": {"clueId": "119241"}, "dynamic_condition_id": null, "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:28.106", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}