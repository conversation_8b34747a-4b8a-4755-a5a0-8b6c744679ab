{"args": [5], "result": [{"id": 12, "systemName": "综合搜索", "apiPath": "/senior/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "dateFormat": "timestamp", "isFieldValAccurate": "", "fieldVal": "{\"auth_account\":\"#{account}\"}", "pageSize": 20, "tags": "{}", "filterId": 24, "onPage": 1, "aggsField": "auth_account", "startTime": 112233, "multiSortField": "{\"_score\":1}", "resultShowType": 1, "aggregationField": "auth_account", "endTime": 445566, "fieldList": ["auth_account"], "timeLabel": "0", "resultType": 1, "containsDetail": true, "aggQueryType": 3, "resourceTableNames": ["deye_v64_remotectrl", "deye_v64_mobilenetradius", "deye_v64_travel", "deye_v64_news", "deye_v64_other", "deye_v64_lbs", "deye_v64_ftp", "deye_v64_shopping", "deye_v64_tool", "deye_v64_voip", "deye_v64_email", "deye_v64_engine", "deye_v64_multimedia", "deye_v64_im", "deye_v64_vpn", "deye_v64_entertainment", "deye_v64_sns", "deye_v64_telnet", "deye_v64_terminal", "deye_v64_finance", "deye_v64_http"], "aggs": ""}, "responseMapping": {"getKey": ["Email", "Engine", "Entertainment", "FTP", "Finance", "HTTP", "IM", "Location", "Multimedia", "News", "Other", "RemoteCTRL", "SNS", "Shopping", "Telnet", "Terminal", "Tool", "Travel", "VPN", "AppCall"], "data.tagInfo.tagCount.协议类型": 2, "conditionKey": [], "is_kv": 1}, "metricId": 5}, {"id": 13, "systemName": "全息档案", "apiPath": "/common_arc_detail/lis/get_distribution.json", "method": "POST", "paramsTemplate": {"arcId": "#{arcId}", "archiveType": "5", "dateFormat": "stringDate", "startDay": "#{startDay}", "arcAccount": "#{account}", "endDay": "#{endDay}", "dataType": "0", "dateOption": 0, "arcAccountType": "1020004", "arcType": "5"}, "responseMapping": {"getKey": ["dataTypeName", "behaviorNum"], "data": 1, "conditionKey": [], "is_kv": 1}, "metricId": 5}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:01:34.908", "argsType": ["java.lang.Integer"]}