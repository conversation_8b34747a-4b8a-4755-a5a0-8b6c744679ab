{"args": [9], "result": [{"id": 23, "systemName": "综合搜索", "apiPath": "/senior/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "dateFormat": "timestamp", "isFieldValAccurate": "", "fieldVal": "{\"resource_table_name\":\"(deye_v64_mobilenetradius)\",\"auth_account\":\"#{account}\"}", "pageSize": 20, "tags": "{}", "filterId": 3, "onPage": 1, "aggsField": "", "startTime": 112233, "multiSortField": "{\"_score\":1}", "resultShowType": 1, "endTime": 445566, "fieldList": ["resource_table_name", "AND", "auth_account"], "timeLabel": "0", "containsDetail": true, "resourceTableNames": ["deye_v64_mobilenetradius", "deye_v64_fixednetradius"], "aggs": ""}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 9}, {"id": 24, "systemName": "全息档案", "apiPath": "/phone_arc_detail/authentication_billing/get_data_detail.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "dateOption": 0, "arcAccountType": "1020004", "isAll": 0, "arcType": "5", "keyWord": "", "archiveType": "5", "onPage": 1, "size": 100, "sortType": "", "arcAccount": "#{account}", "endDay": "#{endDay}", "sortField": "", "action": 99, "networkType": 99}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 9}, {"id": 25, "systemName": "案件管理", "apiPath": "/clue_hit/page_list.json", "method": "POST", "paramsTemplate": {"noContentFilter": 0, "protocolGroupCode": 2201, "dateFormat": "timestamp", "protocolGroupName": "Mobile RADIUS(120)", "clueId": 333333, "secFilterParam": {}, "isReadNum": 0, "currentTime": 0, "unReadNum": 120, "total": 120, "onPage": 1, "size": 30, "clueName": "#{clueName}", "protocolCode": "", "caseId": 111111, "caseName": "#{caseName}", "filterNum": 0, "objectName": "#{objectName}", "startTime": 112233, "endTime": 445566, "objectId": 222222, "similarFilter": 0, "hitTotal": 120}, "responseMapping": {"getKey": [], "conditionKey": [], "data.hitTotal": 0, "is_kv": 0}, "metricId": 9}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:01:52.235", "argsType": ["java.lang.Integer"]}