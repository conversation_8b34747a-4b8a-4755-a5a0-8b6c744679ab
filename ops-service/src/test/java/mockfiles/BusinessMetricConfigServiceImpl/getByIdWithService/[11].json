{"args": [11], "result": {"id": 11, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "访问网络次数", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内Fixed RADIUS账号作为认证账号出现在LIS协议数据中的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, "method": "getByIdWithService", "class": "BusinessMetricConfigServiceImpl", "timestamp": "2025-04-15T21:01:55.259", "argsType": ["java.lang.Integer"]}