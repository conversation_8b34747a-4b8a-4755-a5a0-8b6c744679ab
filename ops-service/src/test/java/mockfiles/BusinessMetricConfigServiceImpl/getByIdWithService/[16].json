{"args": [16], "result": {"id": 16, "metricModelName": "Fixed RADIUS数据差异分析", "metricName": "上网明细", "compareSystems": "综合搜索,全息档案", "description": "特定时间范围内，某个Fixed RADIUS账号的上网LIS明细", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, "method": "getByIdWithService", "class": "BusinessMetricConfigServiceImpl", "timestamp": "2025-04-15T21:02:13.125", "argsType": ["java.lang.Integer"]}