package com.semptian.service.impl;

import com.semptian.component.DorisStreamLoadUtil;
import com.semptian.entity.BusinessMetricConfig;
import com.semptian.entity.BusinessSampleConfig;
import com.semptian.service.BusinessAccountConfigService;
import com.semptian.service.BusinessMetricConfigService;
import com.semptian.service.BusinessSampleConfigService;
import com.semptian.service.constants.TestConstants;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(cn.hutool.http.HttpUtil.class)
@Slf4j
public class InterSystemDataSampleServiceImplTest {
    @Spy
    private RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();

    @Mock
    private ValueOperations<String, Object> valueOperations;
    @Mock
    private BusinessMetricConfigService businessMetricConfigService;

    @InjectMocks
    private InterSystemDataProducerServiceImpl service;

    @Mock
    private BusinessAccountConfigService businessAccountConfigService;

    @Getter
    @Mock
    private DorisStreamLoadUtil dorisStreamLoad;

    @Mock
    private BusinessSampleConfigService businessSampleConfigService; // 添加对 businessSampleConfigService 的模拟

    @Before
    public void setUp() throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        Class<?> tokenCacheEntryClass = Class.forName(TestConstants.INTERNAL_CACHE_CLASS_NAME);
        Constructor<?> constructor = tokenCacheEntryClass.getDeclaredConstructor(String.class, long.class);
        constructor.setAccessible(true);
        Object tokenCacheEntry = constructor.newInstance("unit_test_token", 666666666666L);
        when(valueOperations.get(anyString())).thenReturn(tokenCacheEntry);
    }

    @Test
    public void testProduceSampleAccount() {
        // 1. 准备测试数据
        int maxSampleCount = 100;
        List<BusinessMetricConfig> metricConfigs = new ArrayList<>();
        BusinessMetricConfig metricConfig = BusinessMetricConfig.builder().id(1L).build();
        metricConfigs.add(metricConfig);

        List<BusinessSampleConfig> sampleConfigs = new ArrayList<>();
        BusinessSampleConfig sampleConfig1 = BusinessSampleConfig.builder().id(1L).metricId(1L).sampleWeight(30).dynamicSql("SELECT account FROM user_table").enable(true).build();

        BusinessSampleConfig sampleConfig2 = BusinessSampleConfig.builder().id(2L).metricId(1L).sampleWeight(70).dynamicSql("SELECT account FROM admin_table").enable(true).build();
        sampleConfigs.add(sampleConfig1);
        sampleConfigs.add(sampleConfig2);

        // 模拟服务层返回
        when(businessMetricConfigService.list()).thenReturn(metricConfigs);
        when(businessSampleConfigService.getValidConfigsByMetricId(1L)).thenReturn(sampleConfigs);
        when(businessSampleConfigService.executeDynamicSampling("SELECT account FROM user_table")).thenReturn(Arrays.asList("user1", "user2", "user3"));
        when(businessSampleConfigService.executeDynamicSampling("SELECT account FROM admin_table")).thenReturn(Arrays.asList("admin1", "admin2", "admin3", "admin4", "admin5"));

        // 2. 调用被测方法
        service.produceSampleAccount(maxSampleCount);

    }

}
