package com.semptian.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

@Getter
public enum ModuleNameEnum {
    DATA_COLLECTION(1, "数据采集分析"),
    DATA_MONITOR(2, "数据监控"),
    BUSINESS_DATA(3, "业务数据");

    @EnumValue
    private final int code;
    private final String description;

    ModuleNameEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
}