package com.semptian.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.DetailQueryConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 明细查询配置Mapper
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Mapper
@DS("mysql")
public interface DetailQueryConfigMapper extends BaseMapper<DetailQueryConfig> {

    /**
     * 根据指标ID获取查询配置
     *
     * @param metricId 指标ID
     * @return 查询配置
     */
    @Select("SELECT * FROM tb_ops_detail_query_config WHERE metric_id = #{metricId} LIMIT 1")
    DetailQueryConfig getByMetricId(@Param("metricId") Integer metricId);
}
