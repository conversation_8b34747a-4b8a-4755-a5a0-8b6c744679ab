package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@TableName("tb_ops_business_metric_config")
@AllArgsConstructor
@NoArgsConstructor
public class BusinessMetricConfig {

    @TableId
    private Long id;

    private String metricModelName;

    private String metricName;

    private String compareSystems;

    private String description;

    private Double threshold;

    private String checkInterval;

    private Status status;

    public enum Status {
        ACTIVE("ACTIVE"), INACTIVE("INACTIVE");

        @EnumValue
        private final String code;

        Status(String code) {
            this.code = code;
        }
    }
}