package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/18
 * @Description
 **/
@Data
@TableName("tb_portal_user_manage_org")
public class UserManageOrgEntity {

    @TableId(type = IdType.AUTO)
    private String id;

    @TableField("user_id")
    private Long userId;

    @TableField("org_id")
    private String orgId;

    @TableField("create_time")
    private Long createTime;
}
