package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/1
 * @Description
 **/
@Data
@TableName("tb_portal_app_type")
public class ApplicationTypeEntity {

    /**
     * 应用分类id
     */
    @TableId(type = IdType.UUID)
    private String id;

    /**
     * 应用分类名称
     */
    @TableField("type_name")
    private String appName;

    /**
     * 父id
     */
    @TableField("parent_id")
    private Integer parentId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 创建者
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Long modifyTime;

    /**
     * 修改者
     */
    @TableField("modify_user")
    private String modifyUser;

    /**
     * 删除状态  0:未删除  1:已删除
     */
    @TableField("del_status")
    private Integer delStatus;

    /**
     * 排序字段
     */
    @TableField("`order`")
    private Integer order;
}
