package com.semptian.common;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "common")
@Data
/**

 * <AUTHOR>
 * @date 2020/2/28 11:56
 */
public class UpdateUserCommon {

    private Boolean isHomeTest;

    private String updateUserTime;

    private String ipOrPort;

    private String userList;

    private String orgList;

    private String roleList;

    private String token;

    private String userName;

    private String password;

    private String loginUrl;

    private String saveToken;

    private String getUserMessageByToken;

    private String getUserRoleByToken;

    private String getSoftwareByRoleId;

    private String getFunctionByRoleId;

    private String getUserMenuByToken;

    private String loginOutUrl;
}
